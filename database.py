"""
Database management for the ERP application
"""
import sqlite3
import hashlib
import json
from datetime import datetime, timedelta
from pathlib import Path
from config import Config

class DatabaseManager:
    def __init__(self, db_path=None):
        self.db_path = db_path or Config.DATABASE_PATH
        self.init_database()
    
    def get_connection(self):
        """Get database connection with foreign key support"""
        conn = sqlite3.connect(self.db_path)
        conn.execute("PRAGMA foreign_keys = ON")
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users and Authentication
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT UNIQUE,
                full_name TEXT NOT NULL,
                role_id INTEGER,
                branch_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                last_login DATETIME,
                failed_login_attempts INTEGER DEFAULT 0,
                account_locked_until DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (role_id) REFERENCES roles (id),
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Roles and Permissions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS roles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                permissions TEXT, -- JSON string of permissions
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Branches/Companies
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT UNIQUE NOT NULL,
                address TEXT,
                phone TEXT,
                email TEXT,
                tax_number TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Suppliers
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                tax_number TEXT,
                payment_terms INTEGER DEFAULT 30,
                credit_limit DECIMAL(15,2) DEFAULT 0,
                current_balance DECIMAL(15,2) DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Customers
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                tax_number TEXT,
                payment_terms INTEGER DEFAULT 30,
                credit_limit DECIMAL(15,2) DEFAULT 0,
                current_balance DECIMAL(15,2) DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Product Categories
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                parent_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (parent_id) REFERENCES categories (id)
            )
        ''')
        
        # Products/Inventory
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                unit_of_measure TEXT DEFAULT 'قطعة',
                cost_price DECIMAL(15,2) DEFAULT 0,
                selling_price DECIMAL(15,2) DEFAULT 0,
                current_stock DECIMAL(15,3) DEFAULT 0,
                minimum_stock DECIMAL(15,3) DEFAULT 0,
                maximum_stock DECIMAL(15,3) DEFAULT 0,
                reorder_point DECIMAL(15,3) DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id),
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Purchase Orders
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                po_number TEXT UNIQUE NOT NULL,
                supplier_id INTEGER NOT NULL,
                order_date DATE NOT NULL,
                expected_date DATE,
                status TEXT DEFAULT 'pending',
                subtotal DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                notes TEXT,
                created_by INTEGER,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Purchase Order Items
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                po_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(15,3) NOT NULL,
                unit_price DECIMAL(15,2) NOT NULL,
                total_price DECIMAL(15,2) NOT NULL,
                received_quantity DECIMAL(15,3) DEFAULT 0,
                FOREIGN KEY (po_id) REFERENCES purchase_orders (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # Sales Orders
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                so_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                order_date DATE NOT NULL,
                delivery_date DATE,
                status TEXT DEFAULT 'pending',
                subtotal DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                notes TEXT,
                created_by INTEGER,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Sales Order Items
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sales_order_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                so_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(15,3) NOT NULL,
                unit_price DECIMAL(15,2) NOT NULL,
                discount_percent DECIMAL(5,2) DEFAULT 0,
                total_price DECIMAL(15,2) NOT NULL,
                delivered_quantity DECIMAL(15,3) DEFAULT 0,
                FOREIGN KEY (so_id) REFERENCES sales_orders (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # Invoices
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                invoice_type TEXT NOT NULL, -- 'sales' or 'purchase'
                customer_id INTEGER,
                supplier_id INTEGER,
                invoice_date DATE NOT NULL,
                due_date DATE,
                status TEXT DEFAULT 'pending',
                subtotal DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                paid_amount DECIMAL(15,2) DEFAULT 0,
                balance_due DECIMAL(15,2) DEFAULT 0,
                notes TEXT,
                created_by INTEGER,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Invoice Items
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(15,3) NOT NULL,
                unit_price DECIMAL(15,2) NOT NULL,
                discount_percent DECIMAL(5,2) DEFAULT 0,
                total_price DECIMAL(15,2) NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # Chart of Accounts
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_code TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                account_type TEXT NOT NULL, -- 'asset', 'liability', 'equity', 'revenue', 'expense'
                parent_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES accounts (id),
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Journal Entries
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_number TEXT UNIQUE NOT NULL,
                entry_date DATE NOT NULL,
                description TEXT NOT NULL,
                reference TEXT,
                total_debit DECIMAL(15,2) DEFAULT 0,
                total_credit DECIMAL(15,2) DEFAULT 0,
                created_by INTEGER,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Journal Entry Lines
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entry_lines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_id INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                debit_amount DECIMAL(15,2) DEFAULT 0,
                credit_amount DECIMAL(15,2) DEFAULT 0,
                description TEXT,
                FOREIGN KEY (entry_id) REFERENCES journal_entries (id) ON DELETE CASCADE,
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
        ''')
        
        # Employees
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_number TEXT UNIQUE NOT NULL,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                address TEXT,
                hire_date DATE,
                department TEXT,
                position TEXT,
                salary DECIMAL(15,2),
                is_active BOOLEAN DEFAULT 1,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        # Notifications
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                notification_type TEXT DEFAULT 'info',
                is_read BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Audit Log
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                table_name TEXT,
                record_id INTEGER,
                old_values TEXT, -- JSON
                new_values TEXT, -- JSON
                ip_address TEXT,
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # System Settings
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                updated_by INTEGER,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES users (id)
            )
        ''')
        
        # Stock Movements
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
                quantity DECIMAL(15,3) NOT NULL,
                unit_cost DECIMAL(15,2),
                reference_type TEXT, -- 'purchase', 'sale', 'adjustment', 'transfer'
                reference_id INTEGER,
                notes TEXT,
                created_by INTEGER,
                branch_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (branch_id) REFERENCES branches (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Insert default data
        self.insert_default_data()
    
    def insert_default_data(self):
        """Insert default data for the system"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Default branch
        cursor.execute('''
            INSERT OR IGNORE INTO branches (id, name, code, address, is_active)
            VALUES (1, 'الفرع الرئيسي', 'MAIN', 'العنوان الرئيسي', 1)
        ''')
        
        # Default roles
        admin_permissions = {
            "users": ["create", "read", "update", "delete"],
            "suppliers": ["create", "read", "update", "delete"],
            "customers": ["create", "read", "update", "delete"],
            "products": ["create", "read", "update", "delete"],
            "purchases": ["create", "read", "update", "delete"],
            "sales": ["create", "read", "update", "delete"],
            "invoices": ["create", "read", "update", "delete"],
            "accounting": ["create", "read", "update", "delete"],
            "reports": ["read"],
            "settings": ["update"],
            "backup": ["create", "restore"]
        }
        
        user_permissions = {
            "suppliers": ["read"],
            "customers": ["create", "read", "update"],
            "products": ["read"],
            "purchases": ["create", "read"],
            "sales": ["create", "read", "update"],
            "invoices": ["create", "read", "update"],
            "reports": ["read"]
        }
        
        cursor.execute('''
            INSERT OR IGNORE INTO roles (id, name, description, permissions)
            VALUES (1, 'مدير النظام', 'صلاحيات كاملة للنظام', ?)
        ''', (json.dumps(admin_permissions, ensure_ascii=False),))
        
        cursor.execute('''
            INSERT OR IGNORE INTO roles (id, name, description, permissions)
            VALUES (2, 'مستخدم عادي', 'صلاحيات محدودة للعمليات اليومية', ?)
        ''', (json.dumps(user_permissions, ensure_ascii=False),))
        
        # Default admin user
        admin_password = self.hash_password("admin123")
        cursor.execute('''
            INSERT OR IGNORE INTO users (id, username, password_hash, full_name, role_id, branch_id, is_active)
            VALUES (1, 'admin', ?, 'مدير النظام', 1, 1, 1)
        ''', (admin_password,))
        
        # Default accounts
        default_accounts = [
            ('1000', 'الأصول', 'asset', None),
            ('1100', 'الأصول المتداولة', 'asset', 1),
            ('1110', 'النقدية', 'asset', 2),
            ('1120', 'العملاء', 'asset', 2),
            ('1130', 'المخزون', 'asset', 2),
            ('2000', 'الخصوم', 'liability', None),
            ('2100', 'الخصوم المتداولة', 'liability', 5),
            ('2110', 'الموردون', 'liability', 6),
            ('3000', 'حقوق الملكية', 'equity', None),
            ('3100', 'رأس المال', 'equity', 8),
            ('4000', 'الإيرادات', 'revenue', None),
            ('4100', 'إيرادات المبيعات', 'revenue', 10),
            ('5000', 'المصروفات', 'expense', None),
            ('5100', 'تكلفة البضاعة المباعة', 'expense', 12),
            ('5200', 'مصروفات التشغيل', 'expense', 12)
        ]
        
        for i, (code, name, acc_type, parent_id) in enumerate(default_accounts, 1):
            cursor.execute('''
                INSERT OR IGNORE INTO accounts (id, account_code, account_name, account_type, parent_id, branch_id)
                VALUES (?, ?, ?, ?, ?, 1)
            ''', (i, code, name, acc_type, parent_id))
        
        # Default system settings
        default_settings = [
            ('company_name', 'شركة الأعمال المتكاملة', 'اسم الشركة'),
            ('currency', 'ريال سعودي', 'العملة الافتراضية'),
            ('tax_rate', '15', 'معدل الضريبة %'),
            ('fiscal_year_start', '01-01', 'بداية السنة المالية'),
            ('auto_backup', '1', 'النسخ الاحتياطي التلقائي'),
            ('notification_email', '', 'بريد الإشعارات'),
            ('low_stock_threshold', '10', 'حد المخزون المنخفض')
        ]
        
        for key, value, desc in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            ''', (key, value, desc))
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password):
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password, hash_value):
        """Verify password against hash"""
        return self.hash_password(password) == hash_value
    
    def authenticate_user(self, username, password):
        """Authenticate user and return user data"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT u.*, r.name as role_name, r.permissions, b.name as branch_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            LEFT JOIN branches b ON u.branch_id = b.id
            WHERE u.username = ? AND u.is_active = 1
        ''', (username,))
        
        user = cursor.fetchone()
        
        if not user:
            conn.close()
            return None
        
        # Check if account is locked
        if user['account_locked_until'] and datetime.now() < datetime.fromisoformat(user['account_locked_until']):
            conn.close()
            return None
        
        # Verify password
        if not self.verify_password(password, user['password_hash']):
            # Increment failed attempts
            cursor.execute('''
                UPDATE users SET failed_login_attempts = failed_login_attempts + 1
                WHERE id = ?
            ''', (user['id'],))
            
            # Lock account after max attempts
            if user['failed_login_attempts'] + 1 >= Config.MAX_LOGIN_ATTEMPTS:
                lock_until = datetime.now() + timedelta(minutes=30)
                cursor.execute('''
                    UPDATE users SET account_locked_until = ?
                    WHERE id = ?
                ''', (lock_until.isoformat(), user['id']))
            
            conn.commit()
            conn.close()
            return None
        
        # Reset failed attempts and update last login
        cursor.execute('''
            UPDATE users SET 
                failed_login_attempts = 0,
                account_locked_until = NULL,
                last_login = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (user['id'],))
        
        conn.commit()
        conn.close()
        
        return dict(user)
    
    def log_activity(self, user_id, action, table_name=None, record_id=None, 
                    old_values=None, new_values=None, ip_address=None):
        """Log user activity for audit trail"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO audit_log (user_id, action, table_name, record_id, 
                                 old_values, new_values, ip_address)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, action, table_name, record_id,
              json.dumps(old_values) if old_values else None,
              json.dumps(new_values) if new_values else None,
              ip_address))
        
        conn.commit()
        conn.close()
    
    def get_dashboard_data(self, branch_id=None):
        """Get dashboard statistics"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        branch_filter = "WHERE branch_id = ?" if branch_id else ""
        branch_params = (branch_id,) if branch_id else ()
        
        # Total sales this month
        cursor.execute(f'''
            SELECT COALESCE(SUM(total_amount), 0) as total_sales
            FROM sales_orders 
            WHERE strftime('%Y-%m', order_date) = strftime('%Y-%m', 'now')
            {branch_filter}
        ''', branch_params)
        total_sales = cursor.fetchone()['total_sales']
        
        # Pending invoices
        cursor.execute(f'''
            SELECT COUNT(*) as pending_invoices
            FROM invoices 
            WHERE status = 'pending'
            {branch_filter}
        ''', branch_params)
        pending_invoices = cursor.fetchone()['pending_invoices']
        
        # Low stock items
        cursor.execute(f'''
            SELECT COUNT(*) as low_stock_items
            FROM products 
            WHERE current_stock <= minimum_stock AND is_active = 1
            {branch_filter}
        ''', branch_params)
        low_stock = cursor.fetchone()['low_stock_items']
        
        # Active users today
        cursor.execute('''
            SELECT COUNT(*) as active_users
            FROM users 
            WHERE DATE(last_login) = DATE('now') AND is_active = 1
        ''')
        active_users = cursor.fetchone()['active_users']
        
        conn.close()
        
        return {
            'total_sales': total_sales,
            'pending_invoices': pending_invoices,
            'low_stock_items': low_stock,
            'active_users': active_users
        }
