"""
Enhanced Dashboard with Professional Design
Modern, attractive, and responsive interface
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

from gui.base_window import BaseWindow
from gui.components import DataTable, show_message
from gui.modern_components import (
    ModernCard, ModernButton, ModernMetricCard, 
    ModernProgressBar, ModernNotification, MODERN_COLORS, MODERN_FONTS
)
from gui.theme_manager import get_colors, get_fonts, create_styled_button, create_styled_frame
from utils import get_text, is_rtl, format_arabic_text, NumberUtils, DateUtils
from config import Config
from auth import auth_manager
from database import DatabaseManager

class EnhancedDashboard(BaseWindow):
    """Enhanced dashboard with modern professional design"""
    
    def __init__(self):
        super().__init__(
            title=get_text('dashboard.title'),
            width=1600,
            height=1000
        )
        
        # Apply modern styling
        self.colors = get_colors()
        self.fonts = get_fonts()
        self.configure(fg_color=self.colors['bg_secondary'])
        
        self.db = DatabaseManager()
        self.metrics_cards = {}
        self.charts = {}
        
        # Setup the enhanced interface
        self.setup_enhanced_interface()
        
        # Load data
        self.load_dashboard_data()
        
        # Setup auto-refresh
        self.setup_auto_refresh()
    
    def setup_enhanced_interface(self):
        """Setup the enhanced dashboard interface"""
        # Main container with modern styling
        self.main_container = create_styled_frame(self)
        self.main_container.pack(fill="both", expand=True, padx=15, pady=15)
        
        # Configure responsive grid
        self.main_container.grid_rowconfigure(2, weight=1)  # Charts row
        self.main_container.grid_rowconfigure(3, weight=1)  # Content row
        self.main_container.grid_columnconfigure(1, weight=2)  # Main content
        self.main_container.grid_columnconfigure(2, weight=1)  # Sidebar
        
        # Create enhanced sections
        self.create_enhanced_header()
        self.create_enhanced_navigation()
        self.create_enhanced_metrics()
        self.create_enhanced_charts()
        self.create_enhanced_sidebar()
        self.create_enhanced_footer()
    
    def create_enhanced_header(self):
        """Create enhanced header with gradient and animations"""
        # Header container with gradient effect
        header_container = ctk.CTkFrame(
            self.main_container,
            fg_color=self.colors['primary'],
            corner_radius=20,
            height=140
        )
        header_container.grid(row=0, column=0, columnspan=3, sticky="ew", padx=5, pady=(0, 10))
        header_container.grid_propagate(False)
        header_container.grid_columnconfigure(1, weight=1)
        
        # Left section - User info with avatar
        left_section = ctk.CTkFrame(header_container, fg_color="transparent")
        left_section.grid(row=0, column=0, sticky="nsw", padx=25, pady=20)
        
        # User avatar (using emoji for now)
        avatar_frame = ctk.CTkFrame(
            left_section,
            fg_color=self.colors['white'],
            corner_radius=35,
            width=70,
            height=70
        )
        avatar_frame.pack(side="left", padx=(0, 20))
        avatar_frame.pack_propagate(False)
        
        avatar_label = ctk.CTkLabel(
            avatar_frame,
            text="👤",
            font=ctk.CTkFont(size=32),
            text_color=self.colors['primary']
        )
        avatar_label.place(relx=0.5, rely=0.5, anchor="center")
        
        # User info
        user_info_frame = ctk.CTkFrame(left_section, fg_color="transparent")
        user_info_frame.pack(side="left", fill="y")
        
        # Welcome message
        user_name = auth_manager.get_user_name()
        welcome_text = f"مرحباً {user_name} 👋"
        if is_rtl():
            welcome_text = format_arabic_text(welcome_text)
        
        welcome_label = ctk.CTkLabel(
            user_info_frame,
            text=welcome_text,
            font=self.fonts.get('heading', ctk.CTkFont(size=24, weight="bold")),
            text_color=self.colors['white']
        )
        welcome_label.pack(anchor="w", pady=(5, 0))
        
        # User role and status
        role_text = f"🎯 {auth_manager.get_user_role()}"
        branch = auth_manager.get_user_branch()
        if branch:
            role_text += f" | 🏢 {branch['name']}"
        
        role_label = ctk.CTkLabel(
            user_info_frame,
            text=role_text,
            font=self.fonts.get('small', ctk.CTkFont(size=14)),
            text_color=self.colors['light']
        )
        role_label.pack(anchor="w", pady=(2, 0))
        
        # Online status
        status_frame = ctk.CTkFrame(user_info_frame, fg_color="transparent")
        status_frame.pack(anchor="w", pady=(5, 0))
        
        status_indicator = ctk.CTkLabel(
            status_frame,
            text="🟢",
            font=ctk.CTkFont(size=12)
        )
        status_indicator.pack(side="left")
        
        status_label = ctk.CTkLabel(
            status_frame,
            text="متصل الآن - Online",
            font=self.fonts.get('caption', ctk.CTkFont(size=12)),
            text_color=self.colors['light']
        )
        status_label.pack(side="left", padx=(5, 0))\n        \n        # Center section - App branding\n        center_section = ctk.CTkFrame(header_container, fg_color=\"transparent\")\n        center_section.grid(row=0, column=1, sticky=\"ns\", padx=20, pady=20)\n        \n        # App logo and title\n        logo_frame = ctk.CTkFrame(center_section, fg_color=\"transparent\")\n        logo_frame.pack(expand=True)\n        \n        app_logo = ctk.CTkLabel(\n            logo_frame,\n            text=\"🏢\",\n            font=ctk.CTkFont(size=48)\n        )\n        app_logo.pack(pady=(0, 10))\n        \n        app_title = ctk.CTkLabel(\n            logo_frame,\n            text=\"نظام إدارة الأعمال المتكامل\",\n            font=self.fonts.get('title', ctk.CTkFont(size=28, weight=\"bold\")),\n            text_color=self.colors['white']\n        )\n        app_title.pack()\n        \n        app_subtitle = ctk.CTkLabel(\n            logo_frame,\n            text=\"Integrated Business Management System\",\n            font=self.fonts.get('body', ctk.CTkFont(size=16)),\n            text_color=self.colors['light']\n        )\n        app_subtitle.pack(pady=(5, 0))\n        \n        # Right section - Quick stats and actions\n        right_section = ctk.CTkFrame(header_container, fg_color=\"transparent\")\n        right_section.grid(row=0, column=2, sticky=\"nse\", padx=25, pady=20)\n        \n        # Current time and date\n        time_frame = ctk.CTkFrame(\n            right_section,\n            fg_color=self.colors['white'],\n            corner_radius=15\n        )\n        time_frame.pack(fill=\"x\", pady=(0, 10))\n        \n        current_time = DateUtils.format_datetime(DateUtils.get_current_datetime())\n        time_label = ctk.CTkLabel(\n            time_frame,\n            text=f\"🕐 {current_time}\",\n            font=self.fonts.get('small', ctk.CTkFont(size=14)),\n            text_color=self.colors['text_primary']\n        )\n        time_label.pack(padx=15, pady=10)\n        \n        # Quick action buttons\n        actions_frame = ctk.CTkFrame(right_section, fg_color=\"transparent\")\n        actions_frame.pack(fill=\"x\")\n        \n        # Notifications button\n        notif_count = auth_manager.get_unread_notification_count()\n        notif_text = f\"🔔 ({notif_count})\"\n        \n        notif_button = create_styled_button(\n            actions_frame,\n            text=notif_text,\n            style=\"warning\" if notif_count > 0 else \"secondary\",\n            width=80,\n            height=35,\n            command=self.show_notifications\n        )\n        notif_button.pack(side=\"left\", padx=(0, 5))\n        \n        # Settings button\n        settings_button = create_styled_button(\n            actions_frame,\n            text=\"⚙️\",\n            style=\"secondary\",\n            width=35,\n            height=35,\n            command=self.show_settings\n        )\n        settings_button.pack(side=\"left\", padx=5)\n        \n        # Logout button\n        logout_button = create_styled_button(\n            actions_frame,\n            text=\"🚪\",\n            style=\"danger\",\n            width=35,\n            height=35,\n            command=self.logout\n        )\n        logout_button.pack(side=\"right\")\n    \n    def create_enhanced_navigation(self):\n        \"\"\"Create enhanced navigation with modern module cards\"\"\"\n        nav_container = ctk.CTkFrame(\n            self.main_container,\n            fg_color=\"transparent\"\n        )\n        nav_container.grid(row=1, column=0, columnspan=3, sticky=\"ew\", padx=5, pady=5)\n        \n        # Navigation title\n        nav_title = ctk.CTkLabel(\n            nav_container,\n            text=\"📊 الوحدات المتاحة - Available Modules\",\n            font=self.fonts.get('subheading', ctk.CTkFont(size=20, weight=\"bold\")),\n            text_color=self.colors['text_primary']\n        )\n        nav_title.pack(pady=(0, 15))\n        \n        # Modules container with modern cards\n        modules_container = create_styled_frame(nav_container)\n        modules_container.pack(fill=\"x\", padx=10)\n        \n        # Scrollable modules frame\n        modules_scroll = ctk.CTkScrollableFrame(\n            modules_container,\n            orientation=\"horizontal\",\n            height=200,\n            fg_color=\"transparent\"\n        )\n        modules_scroll.pack(fill=\"x\", padx=20, pady=20)\n        \n        # Enhanced module data with more details\n        modules_data = [\n            {\n                'icon': '👥', 'title': 'إدارة العملاء', 'subtitle': 'Customer Management',\n                'description': 'إدارة بيانات العملاء والعلاقات', 'command': self.open_customers,\n                'ready': True, 'color': self.colors['primary'], 'count': '150+'\n            },\n            {\n                'icon': '🏢', 'title': 'إدارة الموردين', 'subtitle': 'Supplier Management', \n                'description': 'إدارة الموردين والمشتريات', 'command': self.open_suppliers,\n                'ready': True, 'color': self.colors['secondary'], 'count': '75+'\n            },\n            {\n                'icon': '📦', 'title': 'إدارة المخزون', 'subtitle': 'Inventory Management',\n                'description': 'تتبع المنتجات والمخزون', 'command': self.open_inventory,\n                'ready': True, 'color': self.colors['info'], 'count': '500+'\n            },\n            {\n                'icon': '💰', 'title': 'إدارة المبيعات', 'subtitle': 'Sales Management',\n                'description': 'طلبات البيع والفواتير', 'command': self.open_sales,\n                'ready': True, 'color': self.colors['success'], 'count': '200+'\n            },\n            {\n                'icon': '🛒', 'title': 'إدارة المشتريات', 'subtitle': 'Purchase Management',\n                'description': 'طلبات الشراء والموردين', 'command': self.open_purchasing,\n                'ready': False, 'color': self.colors['warning'], 'count': 'قريباً'\n            },\n            {\n                'icon': '📊', 'title': 'التقارير المالية', 'subtitle': 'Financial Reports',\n                'description': 'تقارير شاملة ومفصلة', 'command': self.open_reports,\n                'ready': False, 'color': self.colors['accent'], 'count': 'قريباً'\n            }\n        ]\n        \n        for i, module in enumerate(modules_data):\n            self.create_enhanced_module_card(modules_scroll, module, i)\n    \n    def create_enhanced_module_card(self, parent, module_data, index):\n        \"\"\"Create enhanced module card with animations and details\"\"\"\n        # Card container with hover effects\n        card_container = ctk.CTkFrame(\n            parent,\n            fg_color=self.colors['white'],\n            corner_radius=20,\n            border_width=2,\n            border_color=module_data['color'] if module_data['ready'] else self.colors['gray'],\n            width=280,\n            height=160\n        )\n        card_container.pack(side=\"left\", padx=10, pady=5)\n        card_container.pack_propagate(False)\n        \n        # Header with status indicator\n        header_frame = ctk.CTkFrame(\n            card_container,\n            fg_color=module_data['color'] if module_data['ready'] else self.colors['gray'],\n            corner_radius=15,\n            height=50\n        )\n        header_frame.pack(fill=\"x\", padx=10, pady=(10, 5))\n        header_frame.pack_propagate(False)\n        \n        # Icon and status\n        icon_label = ctk.CTkLabel(\n            header_frame,\n            text=module_data['icon'],\n            font=ctk.CTkFont(size=24)\n        )\n        icon_label.pack(side=\"left\", padx=15, pady=12)\n        \n        # Count/Status\n        count_label = ctk.CTkLabel(\n            header_frame,\n            text=module_data['count'],\n            font=self.fonts.get('small', ctk.CTkFont(size=12, weight=\"bold\")),\n            text_color=self.colors['white']\n        )\n        count_label.pack(side=\"right\", padx=15, pady=12)\n        \n        # Content area\n        content_frame = ctk.CTkFrame(card_container, fg_color=\"transparent\")\n        content_frame.pack(fill=\"both\", expand=True, padx=15, pady=5)\n        \n        # Title\n        title_label = ctk.CTkLabel(\n            content_frame,\n            text=module_data['title'],\n            font=self.fonts.get('label', ctk.CTkFont(size=14, weight=\"bold\")),\n            text_color=self.colors['text_primary']\n        )\n        title_label.pack(anchor=\"w\", pady=(5, 2))\n        \n        # Subtitle\n        subtitle_label = ctk.CTkLabel(\n            content_frame,\n            text=module_data['subtitle'],\n            font=self.fonts.get('caption', ctk.CTkFont(size=11)),\n            text_color=self.colors['text_secondary']\n        )\n        subtitle_label.pack(anchor=\"w\", pady=(0, 5))\n        \n        # Description\n        desc_label = ctk.CTkLabel(\n            content_frame,\n            text=module_data['description'],\n            font=self.fonts.get('caption', ctk.CTkFont(size=10)),\n            text_color=self.colors['text_secondary'],\n            wraplength=240\n        )\n        desc_label.pack(anchor=\"w\", pady=(0, 10))\n        \n        # Action button\n        button_text = \"🚀 فتح الوحدة\" if module_data['ready'] else \"🔒 قيد التطوير\"\n        button_style = \"primary\" if module_data['ready'] else \"secondary\"\n        \n        action_button = create_styled_button(\n            content_frame,\n            text=button_text,\n            style=button_style,\n            width=200,\n            height=30,\n            command=module_data['command']\n        )\n        action_button.pack(anchor=\"w\")\n        \n        # Add hover effects (simplified)\n        def on_enter(event):\n            card_container.configure(border_color=module_data['color'])\n        \n        def on_leave(event):\n            original_color = module_data['color'] if module_data['ready'] else self.colors['gray']\n            card_container.configure(border_color=original_color)\n        \n        card_container.bind(\"<Enter>\", on_enter)\n        card_container.bind(\"<Leave>\", on_leave)\n    \n    def create_enhanced_metrics(self):\n        \"\"\"Create enhanced metrics with animations and trends\"\"\"\n        metrics_container = ctk.CTkFrame(\n            self.main_container,\n            fg_color=\"transparent\"\n        )\n        metrics_container.grid(row=2, column=0, columnspan=3, sticky=\"ew\", padx=5, pady=5)\n        \n        # Metrics title\n        metrics_title = ctk.CTkLabel(\n            metrics_container,\n            text=\"📈 مؤشرات الأداء الرئيسية - Key Performance Indicators\",\n            font=self.fonts.get('subheading', ctk.CTkFont(size=20, weight=\"bold\")),\n            text_color=self.colors['text_primary']\n        )\n        metrics_title.pack(pady=(0, 15))\n        \n        # Metrics grid\n        metrics_grid = create_styled_frame(metrics_container)\n        metrics_grid.pack(fill=\"x\", padx=10)\n        \n        # Configure responsive grid\n        for i in range(4):\n            metrics_grid.grid_columnconfigure(i, weight=1)\n        \n        # Enhanced metrics data\n        metrics_data = [\n            {\n                'key': 'sales',\n                'title': 'إجمالي المبيعات',\n                'subtitle': 'Total Sales',\n                'value': '0.00',\n                'unit': 'ر.س',\n                'trend': '+12.5%',\n                'icon': '💰',\n                'color': self.colors['success']\n            },\n            {\n                'key': 'orders',\n                'title': 'الطلبات المعلقة',\n                'subtitle': 'Pending Orders',\n                'value': '0',\n                'unit': 'طلب',\n                'trend': '-5.2%',\n                'icon': '📋',\n                'color': self.colors['warning']\n            },\n            {\n                'key': 'inventory',\n                'title': 'قيمة المخزون',\n                'subtitle': 'Inventory Value',\n                'value': '0.00',\n                'unit': 'ر.س',\n                'trend': '+8.1%',\n                'icon': '📦',\n                'color': self.colors['info']\n            },\n            {\n                'key': 'customers',\n                'title': 'العملاء النشطون',\n                'subtitle': 'Active Customers',\n                'value': '0',\n                'unit': 'عميل',\n                'trend': '+15.3%',\n                'icon': '👥',\n                'color': self.colors['primary']\n            }\n        ]\n        \n        for i, metric in enumerate(metrics_data):\n            self.metrics_cards[metric['key']] = self.create_enhanced_metric_card(\n                metrics_grid, metric, i\n            )\n    \n    def create_enhanced_metric_card(self, parent, metric_data, index):\n        \"\"\"Create enhanced metric card with trends and animations\"\"\"\n        # Card container\n        card_container = ctk.CTkFrame(\n            parent,\n            fg_color=self.colors['white'],\n            corner_radius=20,\n            border_width=2,\n            border_color=metric_data['color']\n        )\n        card_container.grid(row=0, column=index, padx=15, pady=20, sticky=\"nsew\")\n        \n        # Header with gradient effect\n        header_frame = ctk.CTkFrame(\n            card_container,\n            fg_color=metric_data['color'],\n            corner_radius=15,\n            height=60\n        )\n        header_frame.pack(fill=\"x\", padx=15, pady=(15, 0))\n        header_frame.pack_propagate(False)\n        \n        # Icon\n        icon_label = ctk.CTkLabel(\n            header_frame,\n            text=metric_data['icon'],\n            font=ctk.CTkFont(size=28),\n            text_color=self.colors['white']\n        )\n        icon_label.pack(side=\"left\", padx=20, pady=15)\n        \n        # Title section\n        title_frame = ctk.CTkFrame(header_frame, fg_color=\"transparent\")\n        title_frame.pack(side=\"right\", fill=\"both\", expand=True, padx=20, pady=10)\n        \n        title_label = ctk.CTkLabel(\n            title_frame,\n            text=metric_data['title'],\n            font=self.fonts.get('small', ctk.CTkFont(size=12, weight=\"bold\")),\n            text_color=self.colors['white'],\n            anchor=\"e\"\n        )\n        title_label.pack(anchor=\"e\")\n        \n        subtitle_label = ctk.CTkLabel(\n            title_frame,\n            text=metric_data['subtitle'],\n            font=self.fonts.get('caption', ctk.CTkFont(size=10)),\n            text_color=self.colors['light'],\n            anchor=\"e\"\n        )\n        subtitle_label.pack(anchor=\"e\")\n        \n        # Value section\n        value_frame = ctk.CTkFrame(card_container, fg_color=\"transparent\")\n        value_frame.pack(fill=\"both\", expand=True, padx=20, pady=15)\n        \n        # Main value with unit\n        value_container = ctk.CTkFrame(value_frame, fg_color=\"transparent\")\n        value_container.pack(expand=True)\n        \n        value_label = ctk.CTkLabel(\n            value_container,\n            text=metric_data['value'],\n            font=self.fonts.get('metric', ctk.CTkFont(size=32, weight=\"bold\")),\n            text_color=metric_data['color']\n        )\n        value_label.pack()\n        \n        unit_label = ctk.CTkLabel(\n            value_container,\n            text=metric_data['unit'],\n            font=self.fonts.get('small', ctk.CTkFont(size=12)),\n            text_color=self.colors['text_secondary']\n        )\n        unit_label.pack(pady=(2, 0))\n        \n        # Trend indicator\n        trend_frame = ctk.CTkFrame(value_frame, fg_color=\"transparent\")\n        trend_frame.pack(fill=\"x\", pady=(10, 0))\n        \n        trend_color = self.colors['success'] if metric_data['trend'].startswith('+') else self.colors['danger']\n        trend_icon = \"📈\" if metric_data['trend'].startswith('+') else \"📉\"\n        \n        trend_label = ctk.CTkLabel(\n            trend_frame,\n            text=f\"{trend_icon} {metric_data['trend']} من الشهر الماضي\",\n            font=self.fonts.get('caption', ctk.CTkFont(size=11)),\n            text_color=trend_color\n        )\n        trend_label.pack()\n        \n        # Progress bar for visual appeal\n        progress_value = abs(float(metric_data['trend'].replace('%', '').replace('+', '').replace('-', '')))\n        progress_bar = ModernProgressBar(\n            value_frame,\n            value=min(progress_value, 100),\n            max_value=100,\n            color=trend_color,\n            show_percentage=False,\n            height=6\n        )\n        progress_bar.pack(fill=\"x\", pady=(5, 0))\n        \n        return {\n            'container': card_container,\n            'value_label': value_label,\n            'trend_label': trend_label,\n            'progress_bar': progress_bar\n        }\n    \n    def create_enhanced_charts(self):\n        \"\"\"Create enhanced charts with modern styling\"\"\"\n        charts_container = ctk.CTkFrame(\n            self.main_container,\n            fg_color=\"transparent\"\n        )\n        charts_container.grid(row=3, column=0, columnspan=2, sticky=\"nsew\", padx=5, pady=5)\n        \n        # Charts title\n        charts_title = ctk.CTkLabel(\n            charts_container,\n            text=\"📊 الرسوم البيانية التفاعلية - Interactive Charts\",\n            font=self.fonts.get('subheading', ctk.CTkFont(size=20, weight=\"bold\")),\n            text_color=self.colors['text_primary']\n        )\n        charts_title.pack(pady=(0, 15))\n        \n        # Charts frame\n        charts_frame = create_styled_frame(charts_container)\n        charts_frame.pack(fill=\"both\", expand=True, padx=10)\n        \n        # Create tabbed charts\n        charts_tabs = ctk.CTkTabview(charts_frame)\n        charts_tabs.pack(fill=\"both\", expand=True, padx=20, pady=20)\n        \n        # Sales chart tab\n        charts_tabs.add(\"📈 المبيعات\")\n        self.create_sales_chart(charts_tabs.tab(\"📈 المبيعات\"))\n        \n        # Inventory chart tab\n        charts_tabs.add(\"📦 المخزون\")\n        self.create_inventory_chart(charts_tabs.tab(\"📦 المخزون\"))\n        \n        # Financial chart tab\n        charts_tabs.add(\"💰 المالية\")\n        self.create_financial_chart(charts_tabs.tab(\"💰 المالية\"))\n    \n    def create_enhanced_sidebar(self):\n        \"\"\"Create enhanced sidebar with quick actions and notifications\"\"\"\n        sidebar_container = ctk.CTkFrame(\n            self.main_container,\n            fg_color=\"transparent\"\n        )\n        sidebar_container.grid(row=3, column=2, sticky=\"nsew\", padx=5, pady=5)\n        \n        # Sidebar title\n        sidebar_title = ctk.CTkLabel(\n            sidebar_container,\n            text=\"⚡ الشريط الجانبي - Sidebar\",\n            font=self.fonts.get('subheading', ctk.CTkFont(size=18, weight=\"bold\")),\n            text_color=self.colors['text_primary']\n        )\n        sidebar_title.pack(pady=(0, 15))\n        \n        # Quick actions section\n        self.create_quick_actions_section(sidebar_container)\n        \n        # Notifications section\n        self.create_notifications_section(sidebar_container)\n        \n        # System status section\n        self.create_system_status_section(sidebar_container)\n    \n    def create_quick_actions_section(self, parent):\n        \"\"\"Create enhanced quick actions section\"\"\"\n        actions_card = ModernCard(\n            parent,\n            title=\"🚀 إجراءات سريعة\",\n            subtitle=\"Quick Actions\",\n            icon=\"⚡\"\n        )\n        actions_card.pack(fill=\"x\", pady=(0, 15))\n        \n        # Actions container\n        actions_container = ctk.CTkFrame(actions_card, fg_color=\"transparent\")\n        actions_container.pack(fill=\"x\", padx=20, pady=(0, 20))\n        \n        # Quick action buttons\n        quick_actions = [\n            {\"text\": \"عميل جديد\", \"icon\": \"👤\", \"command\": self.quick_new_customer, \"style\": \"primary\"},\n            {\"text\": \"طلب بيع\", \"icon\": \"💰\", \"command\": self.quick_new_sale, \"style\": \"success\"},\n            {\"text\": \"تقرير سريع\", \"icon\": \"📊\", \"command\": self.quick_report, \"style\": \"info\"},\n            {\"text\": \"نسخ احتياطي\", \"icon\": \"💾\", \"command\": self.quick_backup, \"style\": \"warning\"}\n        ]\n        \n        for action in quick_actions:\n            button = create_styled_button(\n                actions_container,\n                text=f\"{action['icon']} {action['text']}\",\n                style=action['style'],\n                height=40,\n                command=action['command']\n            )\n            button.pack(fill=\"x\", pady=3)\n    \n    def create_notifications_section(self, parent):\n        \"\"\"Create enhanced notifications section\"\"\"\n        notif_card = ModernCard(\n            parent,\n            title=\"🔔 الإشعارات\",\n            subtitle=\"Notifications\",\n            icon=\"📢\"\n        )\n        notif_card.pack(fill=\"x\", pady=(0, 15))\n        \n        # Notifications container\n        notif_container = ctk.CTkScrollableFrame(\n            notif_card,\n            height=200,\n            fg_color=\"transparent\"\n        )\n        notif_container.pack(fill=\"both\", expand=True, padx=20, pady=(0, 20))\n        \n        # Load and display notifications\n        self.load_notifications(notif_container)\n    \n    def create_system_status_section(self, parent):\n        \"\"\"Create system status section\"\"\"\n        status_card = ModernCard(\n            parent,\n            title=\"🖥️ حالة النظام\",\n            subtitle=\"System Status\",\n            icon=\"⚙️\"\n        )\n        status_card.pack(fill=\"x\")\n        \n        # Status container\n        status_container = ctk.CTkFrame(status_card, fg_color=\"transparent\")\n        status_container.pack(fill=\"x\", padx=20, pady=(0, 20))\n        \n        # System metrics\n        system_metrics = [\n            {\"label\": \"حالة الخادم\", \"value\": \"🟢 متصل\", \"color\": self.colors['success']},\n            {\"label\": \"قاعدة البيانات\", \"value\": \"🟢 نشطة\", \"color\": self.colors['success']},\n            {\"label\": \"المستخدمون النشطون\", \"value\": \"5 مستخدمين\", \"color\": self.colors['info']},\n            {\"label\": \"آخر نسخة احتياطية\", \"value\": \"منذ ساعة\", \"color\": self.colors['warning']}\n        ]\n        \n        for metric in system_metrics:\n            metric_frame = ctk.CTkFrame(status_container, fg_color=\"transparent\")\n            metric_frame.pack(fill=\"x\", pady=2)\n            \n            label = ctk.CTkLabel(\n                metric_frame,\n                text=metric['label'],\n                font=self.fonts.get('caption', ctk.CTkFont(size=11)),\n                text_color=self.colors['text_secondary'],\n                anchor=\"w\"\n            )\n            label.pack(side=\"left\")\n            \n            value = ctk.CTkLabel(\n                metric_frame,\n                text=metric['value'],\n                font=self.fonts.get('caption', ctk.CTkFont(size=11, weight=\"bold\")),\n                text_color=metric['color'],\n                anchor=\"e\"\n            )\n            value.pack(side=\"right\")\n    \n    def create_enhanced_footer(self):\n        \"\"\"Create enhanced footer with system info\"\"\"\n        footer_container = ctk.CTkFrame(\n            self.main_container,\n            fg_color=self.colors['primary'],\n            corner_radius=15,\n            height=50\n        )\n        footer_container.grid(row=4, column=0, columnspan=3, sticky=\"ew\", padx=5, pady=(10, 0))\n        footer_container.grid_propagate(False)\n        footer_container.grid_columnconfigure(1, weight=1)\n        \n        # Left section - Version info\n        version_label = ctk.CTkLabel(\n            footer_container,\n            text=f\"📱 Version {Config.VERSION} | © 2024 ERP System\",\n            font=self.fonts.get('caption', ctk.CTkFont(size=12)),\n            text_color=self.colors['white']\n        )\n        version_label.grid(row=0, column=0, padx=20, pady=15, sticky=\"w\")\n        \n        # Center section - Status\n        status_label = ctk.CTkLabel(\n            footer_container,\n            text=\"🟢 النظام يعمل بشكل طبيعي - System Running Normally\",\n            font=self.fonts.get('caption', ctk.CTkFont(size=12)),\n            text_color=self.colors['light']\n        )\n        status_label.grid(row=0, column=1, pady=15)\n        \n        # Right section - Last update\n        update_label = ctk.CTkLabel(\n            footer_container,\n            text=f\"🔄 آخر تحديث: {DateUtils.format_time(DateUtils.get_current_datetime())}\",\n            font=self.fonts.get('caption', ctk.CTkFont(size=12)),\n            text_color=self.colors['white']\n        )\n        update_label.grid(row=0, column=2, padx=20, pady=15, sticky=\"e\")\n    \n    # Chart creation methods\n    def create_sales_chart(self, parent):\n        \"\"\"Create enhanced sales chart\"\"\"\n        # Create matplotlib figure with modern styling\n        fig = Figure(figsize=(10, 6), dpi=100, facecolor=self.colors['white'])\n        ax = fig.add_subplot(111)\n        \n        # Sample data\n        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']\n        sales = [50000, 65000, 45000, 80000, 75000, 90000]\n        \n        # Create gradient bars\n        bars = ax.bar(months, sales, color=self.colors['primary'], alpha=0.8)\n        \n        # Styling\n        ax.set_title('مبيعات الأشهر الستة الماضية', fontsize=16, fontweight='bold', pad=20)\n        ax.set_ylabel('المبلغ (ر.س)', fontsize=12)\n        ax.grid(True, alpha=0.3)\n        \n        # Remove top and right spines\n        ax.spines['top'].set_visible(False)\n        ax.spines['right'].set_visible(False)\n        \n        # Add value labels on bars\n        for bar in bars:\n            height = bar.get_height()\n            ax.text(bar.get_x() + bar.get_width()/2., height + 1000,\n                   f'{int(height):,}', ha='center', va='bottom', fontsize=10)\n        \n        fig.tight_layout()\n        \n        # Embed in tkinter\n        canvas = FigureCanvasTkAgg(fig, parent)\n        canvas.draw()\n        canvas.get_tk_widget().pack(fill=\"both\", expand=True, padx=20, pady=20)\n    \n    def create_inventory_chart(self, parent):\n        \"\"\"Create enhanced inventory chart\"\"\"\n        fig = Figure(figsize=(10, 6), dpi=100, facecolor=self.colors['white'])\n        ax = fig.add_subplot(111)\n        \n        # Sample data\n        categories = ['إلكترونيات', 'ملابس', 'أثاث', 'كتب', 'أدوات']\n        quantities = [150, 200, 80, 300, 120]\n        colors_list = [self.colors['primary'], self.colors['secondary'], \n                      self.colors['success'], self.colors['warning'], self.colors['info']]\n        \n        # Create pie chart\n        wedges, texts, autotexts = ax.pie(quantities, labels=categories, colors=colors_list,\n                                         autopct='%1.1f%%', startangle=90)\n        \n        ax.set_title('توزيع المخزون حسب الفئة', fontsize=16, fontweight='bold', pad=20)\n        \n        fig.tight_layout()\n        \n        canvas = FigureCanvasTkAgg(fig, parent)\n        canvas.draw()\n        canvas.get_tk_widget().pack(fill=\"both\", expand=True, padx=20, pady=20)\n    \n    def create_financial_chart(self, parent):\n        \"\"\"Create enhanced financial chart\"\"\"\n        fig = Figure(figsize=(10, 6), dpi=100, facecolor=self.colors['white'])\n        ax = fig.add_subplot(111)\n        \n        # Sample data\n        months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']\n        revenue = [80000, 95000, 70000, 110000, 105000, 125000]\n        expenses = [60000, 70000, 55000, 80000, 75000, 85000]\n        profit = [r - e for r, e in zip(revenue, expenses)]\n        \n        # Create line chart\n        ax.plot(months, revenue, marker='o', linewidth=3, label='الإيرادات', color=self.colors['success'])\n        ax.plot(months, expenses, marker='s', linewidth=3, label='المصروفات', color=self.colors['danger'])\n        ax.plot(months, profit, marker='^', linewidth=3, label='الربح', color=self.colors['primary'])\n        \n        ax.set_title('التحليل المالي الشهري', fontsize=16, fontweight='bold', pad=20)\n        ax.set_ylabel('المبلغ (ر.س)', fontsize=12)\n        ax.legend()\n        ax.grid(True, alpha=0.3)\n        \n        # Remove top and right spines\n        ax.spines['top'].set_visible(False)\n        ax.spines['right'].set_visible(False)\n        \n        fig.tight_layout()\n        \n        canvas = FigureCanvasTkAgg(fig, parent)\n        canvas.draw()\n        canvas.get_tk_widget().pack(fill=\"both\", expand=True, padx=20, pady=20)\n    \n    # Data loading and utility methods\n    def load_dashboard_data(self):\n        \"\"\"Load dashboard data with enhanced metrics\"\"\"\n        try:\n            # Load real data from database\n            self.load_sales_metrics()\n            self.load_inventory_metrics()\n            self.load_customer_metrics()\n            self.load_order_metrics()\n            \n        except Exception as e:\n            print(f\"Error loading dashboard data: {e}\")\n            # Load sample data as fallback\n            self.load_sample_data()\n    \n    def load_sample_data(self):\n        \"\"\"Load sample data for demonstration\"\"\"\n        sample_metrics = {\n            'sales': '125,750.00',\n            'orders': '23',\n            'inventory': '89,250.00',\n            'customers': '156'\n        }\n        \n        for key, value in sample_metrics.items():\n            if key in self.metrics_cards:\n                self.update_metric_card(key, value)\n    \n    def load_notifications(self, container):\n        \"\"\"Load and display notifications\"\"\"\n        try:\n            notifications = auth_manager.get_notifications(limit=5)\n            \n            if not notifications:\n                # Show placeholder\n                placeholder = ctk.CTkLabel(\n                    container,\n                    text=\"📭 لا توجد إشعارات جديدة\\nNo new notifications\",\n                    font=self.fonts.get('small', ctk.CTkFont(size=12)),\n                    text_color=self.colors['text_secondary']\n                )\n                placeholder.pack(expand=True, pady=20)\n                return\n            \n            for notification in notifications:\n                self.create_notification_widget(container, notification)\n                \n        except Exception as e:\n            print(f\"Error loading notifications: {e}\")\n    \n    def create_notification_widget(self, parent, notification):\n        \"\"\"Create enhanced notification widget\"\"\"\n        notif_frame = ctk.CTkFrame(\n            parent,\n            fg_color=self.colors['light'] if not notification['is_read'] else self.colors['white'],\n            corner_radius=10,\n            border_width=1,\n            border_color=self.colors['primary'] if not notification['is_read'] else self.colors['border']\n        )\n        notif_frame.pack(fill=\"x\", pady=3)\n        \n        # Content\n        content_frame = ctk.CTkFrame(notif_frame, fg_color=\"transparent\")\n        content_frame.pack(fill=\"x\", padx=10, pady=8)\n        \n        # Icon and title\n        header_frame = ctk.CTkFrame(content_frame, fg_color=\"transparent\")\n        header_frame.pack(fill=\"x\", pady=(0, 5))\n        \n        icon_map = {'info': '📢', 'warning': '⚠️', 'success': '✅', 'error': '❌'}\n        icon = icon_map.get(notification.get('type', 'info'), '📢')\n        \n        title_text = f\"{icon} {notification['title']}\"\n        title_label = ctk.CTkLabel(\n            header_frame,\n            text=title_text,\n            font=self.fonts.get('small', ctk.CTkFont(size=11, weight=\"bold\")),\n            text_color=self.colors['text_primary'],\n            anchor=\"w\"\n        )\n        title_label.pack(side=\"left\")\n        \n        if not notification['is_read']:\n            new_badge = ctk.CTkLabel(\n                header_frame,\n                text=\"🔴\",\n                font=ctk.CTkFont(size=8)\n            )\n            new_badge.pack(side=\"right\")\n        \n        # Message\n        message_label = ctk.CTkLabel(\n            content_frame,\n            text=notification['message'],\n            font=self.fonts.get('caption', ctk.CTkFont(size=10)),\n            text_color=self.colors['text_secondary'],\n            wraplength=200,\n            anchor=\"w\"\n        )\n        message_label.pack(fill=\"x\", pady=(0, 5))\n        \n        # Time\n        time_text = DateUtils.format_datetime(notification['created_at'])\n        time_label = ctk.CTkLabel(\n            content_frame,\n            text=f\"🕐 {time_text}\",\n            font=self.fonts.get('caption', ctk.CTkFont(size=9)),\n            text_color=self.colors['text_light'],\n            anchor=\"w\"\n        )\n        time_label.pack(fill=\"x\")\n        \n        # Click to mark as read\n        if not notification['is_read']:\n            def mark_read(event):\n                auth_manager.mark_notification_read(notification['id'])\n                notif_frame.configure(fg_color=self.colors['white'], border_color=self.colors['border'])\n                new_badge.destroy()\n            \n            notif_frame.bind(\"<Button-1>\", mark_read)\n    \n    def setup_auto_refresh(self):\n        \"\"\"Setup auto-refresh for dashboard data\"\"\"\n        def refresh_data():\n            try:\n                self.load_dashboard_data()\n                # Schedule next refresh\n                self.after(300000, refresh_data)  # 5 minutes\n            except Exception as e:\n                print(f\"Error in auto-refresh: {e}\")\n                # Retry in 1 minute\n                self.after(60000, refresh_data)\n        \n        # Start auto-refresh\n        self.after(60000, refresh_data)  # First refresh after 1 minute\n    \n    def update_metric_card(self, card_name, value):\n        \"\"\"Update metric card with animation\"\"\"\n        if card_name in self.metrics_cards:\n            card = self.metrics_cards[card_name]\n            \n            # Update value with animation effect\n            value_label = card['value_label']\n            original_color = value_label.cget('text_color')\n            \n            # Flash effect\n            value_label.configure(text_color=self.colors['accent'])\n            value_label.configure(text=str(value))\n            \n            # Reset color after delay\n            self.after(500, lambda: value_label.configure(text_color=original_color))\n    \n    # Quick action methods\n    def quick_new_customer(self):\n        \"\"\"Quick new customer action\"\"\"\n        self.open_customers()\n    \n    def quick_new_sale(self):\n        \"\"\"Quick new sale action\"\"\"\n        self.open_sales()\n    \n    def quick_report(self):\n        \"\"\"Quick report action\"\"\"\n        show_message(get_text('common.info'), \"سيتم تطوير التقارير السريعة قريباً\", \"info\")\n    \n    def quick_backup(self):\n        \"\"\"Quick backup action\"\"\"\n        show_message(get_text('common.info'), \"سيتم تطوير النسخ الاحتياطي قريباً\", \"info\")\n    \n    def show_notifications(self):\n        \"\"\"Show notifications window\"\"\"\n        show_message(get_text('common.info'), \"سيتم تطوير نافذة الإشعارات قريباً\", \"info\")\n    \n    def show_settings(self):\n        \"\"\"Show settings window\"\"\"\n        show_message(get_text('common.info'), \"سيتم تطوير نافذة الإعدادات قريباً\", \"info\")\n    \n    def logout(self):\n        \"\"\"Logout user\"\"\"\n        if show_message(get_text('common.confirm'), get_text('common.confirm_logout'), \"question\"):\n            auth_manager.logout()\n            self.destroy()\n    \n    # Module opening methods (same as original dashboard)\n    def open_customers(self):\n        \"\"\"Open customers module\"\"\"\n        try:\n            from modules.customers import CustomersWindow\n            customers_window = CustomersWindow(self)\n            customers_window.grab_set()\n        except ImportError:\n            show_message(get_text('common.info'), get_text('info.module_coming_soon'), \"info\")\n    \n    def open_suppliers(self):\n        \"\"\"Open suppliers module\"\"\"\n        try:\n            from modules.suppliers import SuppliersWindow\n            suppliers_window = SuppliersWindow(self)\n            suppliers_window.grab_set()\n        except ImportError:\n            show_message(get_text('common.info'), get_text('info.module_coming_soon'), \"info\")\n    \n    def open_inventory(self):\n        \"\"\"Open inventory module\"\"\"\n        try:\n            from modules.inventory import InventoryWindow\n            inventory_window = InventoryWindow(self)\n            inventory_window.grab_set()\n        except ImportError:\n            show_message(get_text('common.info'), get_text('info.module_coming_soon'), \"info\")\n    \n    def open_sales(self):\n        \"\"\"Open sales module\"\"\"\n        try:\n            from modules.sales import SalesWindow\n            sales_window = SalesWindow(self)\n            sales_window.grab_set()\n        except ImportError:\n            show_message(get_text('common.info'), get_text('info.module_coming_soon'), \"info\")\n    \n    def open_purchasing(self):\n        \"\"\"Open purchasing module\"\"\"\n        show_message(get_text('common.info'), get_text('info.module_coming_soon'), \"info\")\n    \n    def open_reports(self):\n        \"\"\"Open reports module\"\"\"\n        show_message(get_text('common.info'), get_text('info.module_coming_soon'), \"info\")\n    \n    # Data loading methods (implement based on your database structure)\n    def load_sales_metrics(self):\n        \"\"\"Load sales metrics from database\"\"\"\n        # Implement actual database queries\n        pass\n    \n    def load_inventory_metrics(self):\n        \"\"\"Load inventory metrics from database\"\"\"\n        # Implement actual database queries\n        pass\n    \n    def load_customer_metrics(self):\n        \"\"\"Load customer metrics from database\"\"\"\n        # Implement actual database queries\n        pass\n    \n    def load_order_metrics(self):\n        \"\"\"Load order metrics from database\"\"\"\n        # Implement actual database queries\n        pass\n\nif __name__ == \"__main__\":\n    # Test the enhanced dashboard\n    app = ctk.CTk()\n    dashboard = EnhancedDashboard()\n    app.mainloop()
