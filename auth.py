"""
Authentication and session management for the ERP application
"""
import json
from datetime import datetime, timedelta
from database import DatabaseManager
from utils import get_text, SecurityUtils
from config import Config

class AuthenticationManager:
    def __init__(self):
        self.db = DatabaseManager()
        self.current_user = None
        self.session_start = None
        self.permissions = {}
    
    def login(self, username, password):
        """Authenticate user and start session"""
        # Sanitize inputs
        username = SecurityUtils.sanitize_input(username)
        
        # Authenticate with database
        user_data = self.db.authenticate_user(username, password)
        
        if user_data:
            self.current_user = user_data
            self.session_start = datetime.now()
            
            # Parse permissions
            if user_data.get('permissions'):
                try:
                    self.permissions = json.loads(user_data['permissions'])
                except json.JSONDecodeError:
                    self.permissions = {}
            
            # Log successful login
            self.db.log_activity(
                user_id=user_data['id'],
                action="login",
                ip_address=self.get_client_ip()
            )
            
            return True
        
        return False
    
    def logout(self):
        """End user session"""
        if self.current_user:
            # Log logout
            self.db.log_activity(
                user_id=self.current_user['id'],
                action="logout",
                ip_address=self.get_client_ip()
            )
        
        self.current_user = None
        self.session_start = None
        self.permissions = {}
    
    def is_authenticated(self):
        """Check if user is authenticated"""
        if not self.current_user or not self.session_start:
            return False
        
        # Check session timeout
        session_duration = datetime.now() - self.session_start
        if session_duration.total_seconds() > Config.SESSION_TIMEOUT:
            self.logout()
            return False
        
        return True
    
    def refresh_session(self):
        """Refresh session timestamp"""
        if self.is_authenticated():
            self.session_start = datetime.now()
    
    def get_current_user(self):
        """Get current authenticated user"""
        if self.is_authenticated():
            return self.current_user
        return None
    
    def get_user_id(self):
        """Get current user ID"""
        user = self.get_current_user()
        return user['id'] if user else None
    
    def get_user_name(self):
        """Get current user full name"""
        user = self.get_current_user()
        return user['full_name'] if user else None
    
    def get_user_role(self):
        """Get current user role"""
        user = self.get_current_user()
        return user['role_name'] if user else None
    
    def get_user_branch(self):
        """Get current user branch"""
        user = self.get_current_user()
        return {
            'id': user['branch_id'],
            'name': user['branch_name']
        } if user else None
    
    def has_permission(self, module, action):
        """Check if user has specific permission"""
        if not self.is_authenticated():
            return False
        
        # Super admin has all permissions
        if self.current_user.get('role_name') == 'مدير النظام':
            return True
        
        # Check specific permission
        module_permissions = self.permissions.get(module, [])
        return action in module_permissions
    
    def require_permission(self, module, action):
        """Decorator to require specific permission"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                if not self.has_permission(module, action):
                    raise PermissionError(get_text('error.insufficient_permissions'))
                return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def get_accessible_modules(self):
        """Get list of modules user can access"""
        if not self.is_authenticated():
            return []
        
        # Super admin can access all modules
        if self.current_user.get('role_name') == 'مدير النظام':
            return list(self.permissions.keys()) if self.permissions else [
                'purchasing', 'suppliers', 'customers', 'sales', 'inventory',
                'accounting', 'reports', 'invoices', 'employees', 'notifications',
                'audit', 'backup', 'settings', 'branches'
            ]
        
        # Return modules with any permission
        return [module for module, perms in self.permissions.items() if perms]
    
    def can_access_module(self, module):
        """Check if user can access a module"""
        return module in self.get_accessible_modules()
    
    def get_client_ip(self):
        """Get client IP address (placeholder for future implementation)"""
        return "127.0.0.1"  # Local for now
    
    def change_password(self, old_password, new_password):
        """Change user password"""
        if not self.is_authenticated():
            return False, get_text('error.not_authenticated')
        
        # Verify old password
        if not self.db.verify_password(old_password, self.current_user['password_hash']):
            return False, get_text('error.invalid_old_password')
        
        # Validate new password
        if len(new_password) < Config.PASSWORD_MIN_LENGTH:
            return False, get_text('forms.password_too_short')
        
        # Update password
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        new_hash = self.db.hash_password(new_password)
        cursor.execute('''
            UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (new_hash, self.current_user['id']))
        
        conn.commit()
        conn.close()
        
        # Log password change
        self.db.log_activity(
            user_id=self.current_user['id'],
            action="password_change",
            ip_address=self.get_client_ip()
        )
        
        return True, get_text('success.password_changed')
    
    def get_user_notifications(self, limit=10):
        """Get user notifications"""
        if not self.is_authenticated():
            return []
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM notifications
            WHERE user_id = ? OR user_id IS NULL
            ORDER BY created_at DESC
            LIMIT ?
        ''', (self.current_user['id'], limit))
        
        notifications = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return notifications
    
    def mark_notification_read(self, notification_id):
        """Mark notification as read"""
        if not self.is_authenticated():
            return False
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE notifications SET is_read = 1
            WHERE id = ? AND (user_id = ? OR user_id IS NULL)
        ''', (notification_id, self.current_user['id']))
        
        conn.commit()
        conn.close()
        
        return cursor.rowcount > 0
    
    def get_unread_notification_count(self):
        """Get count of unread notifications"""
        if not self.is_authenticated():
            return 0
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT COUNT(*) as count FROM notifications
            WHERE (user_id = ? OR user_id IS NULL) AND is_read = 0
        ''', (self.current_user['id'],))
        
        result = cursor.fetchone()
        conn.close()
        
        return result['count'] if result else 0

class UserManager:
    def __init__(self):
        self.db = DatabaseManager()
    
    def create_user(self, username, password, full_name, email=None, role_id=None, branch_id=None):
        """Create new user"""
        # Validate inputs
        if not username or not password or not full_name:
            return False, get_text('forms.required_field')
        
        if len(password) < Config.PASSWORD_MIN_LENGTH:
            return False, get_text('forms.password_too_short')
        
        if email and not ValidationUtils.validate_email(email):
            return False, get_text('forms.invalid_email')
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # Check if username exists
            cursor.execute('SELECT id FROM users WHERE username = ?', (username,))
            if cursor.fetchone():
                return False, get_text('error.username_exists')
            
            # Check if email exists
            if email:
                cursor.execute('SELECT id FROM users WHERE email = ?', (email,))
                if cursor.fetchone():
                    return False, get_text('error.email_exists')
            
            # Create user
            password_hash = self.db.hash_password(password)
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, email, role_id, branch_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, password_hash, full_name, email, role_id, branch_id))
            
            user_id = cursor.lastrowid
            conn.commit()
            
            return True, get_text('success.user_created')
            
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()
    
    def update_user(self, user_id, **kwargs):
        """Update user information"""
        if not user_id:
            return False, get_text('error.invalid_user_id')
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        try:
            # Build update query
            update_fields = []
            values = []
            
            for field, value in kwargs.items():
                if field in ['full_name', 'email', 'role_id', 'branch_id', 'is_active']:
                    update_fields.append(f"{field} = ?")
                    values.append(value)
            
            if not update_fields:
                return False, get_text('error.no_fields_to_update')
            
            values.append(user_id)
            
            cursor.execute(f'''
                UPDATE users SET {', '.join(update_fields)}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', values)
            
            conn.commit()
            
            if cursor.rowcount > 0:
                return True, get_text('success.user_updated')
            else:
                return False, get_text('error.user_not_found')
                
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()
    
    def delete_user(self, user_id):
        """Deactivate user (soft delete)"""
        return self.update_user(user_id, is_active=False)
    
    def get_users(self, branch_id=None, active_only=True):
        """Get list of users"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT u.*, r.name as role_name, b.name as branch_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            LEFT JOIN branches b ON u.branch_id = b.id
            WHERE 1=1
        '''
        params = []
        
        if active_only:
            query += " AND u.is_active = 1"
        
        if branch_id:
            query += " AND u.branch_id = ?"
            params.append(branch_id)
        
        query += " ORDER BY u.full_name"
        
        cursor.execute(query, params)
        users = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return users
    
    def get_user_by_id(self, user_id):
        """Get user by ID"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT u.*, r.name as role_name, b.name as branch_name
            FROM users u
            LEFT JOIN roles r ON u.role_id = r.id
            LEFT JOIN branches b ON u.branch_id = b.id
            WHERE u.id = ?
        ''', (user_id,))
        
        user = cursor.fetchone()
        conn.close()
        
        return dict(user) if user else None

# Global authentication manager instance
auth_manager = AuthenticationManager()
