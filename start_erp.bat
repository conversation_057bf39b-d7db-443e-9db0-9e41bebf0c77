@echo off
chcp 65001 >nul
title نظام إدارة الأعمال المتكامل - ERP System

echo.
echo ========================================
echo    نظام إدارة الأعمال المتكامل
echo    Integrated Business Management System
echo ========================================
echo.

echo 🚀 Starting ERP Application...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or higher
    pause
    exit /b 1
)

REM Check if requirements are installed
echo 📦 Checking requirements...
python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing requirements...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install requirements
        pause
        exit /b 1
    )
)

echo ✅ Requirements OK
echo.

REM Start the application
echo 🎯 Launching application...
python main.py

if errorlevel 1 (
    echo.
    echo ❌ Application exited with error
    echo.
    echo 💡 Troubleshooting tips:
    echo 1. Make sure Python 3.8+ is installed
    echo 2. Run: python setup.py
    echo 3. Check error messages above
    echo.
    pause
)

echo.
echo 👋 Application closed
pause
