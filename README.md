# نظام إدارة الأعمال المتكامل - Integrated Business Management System

A comprehensive ERP (Enterprise Resource Planning) application built with Python, featuring bilingual support (Arabic/English), modern GUI, and complete business management modules.

## 🌟 Features

### Core Modules
1. **إدارة المشتريات (Purchasing Management)** - Complete purchase order management
2. **إدارة الموردين (Supplier Management)** - Supplier database and relationship management
3. **إدارة العملاء (Customer Management)** - Customer database and CRM features
4. **إدارة المبيعات (Sales Management)** - Sales order processing and tracking
5. **إدارة المخزون (Inventory Management)** - Stock control and warehouse management
6. **إدارة الحسابات العامة (General Ledger)** - Complete accounting system
7. **التقارير المالية (Financial Reports)** - Comprehensive financial reporting
8. **إدارة الفواتير (Invoice Management)** - Invoice generation and tracking
9. **إدارة الموظفين (Employee Management)** - HR and employee database
10. **دعم تعدد المستخدمين والصلاحيات (User Roles & Permissions)** - Multi-user access control
11. **شاشة دخول آمن (Secure Login)** - Authentication and session management
12. **نظام إشعارات (Notification System)** - Real-time notifications
13. **سجل النشاطات (Audit Log)** - Complete activity tracking
14. **نسخ احتياطي تلقائي (Automatic Backup)** - Data backup and restore
15. **إعدادات عامة للنظام (System Settings)** - Configurable system parameters
16. **دعم تعدد الفروع (Multi-Branch Support)** - Multiple branch/company management
17. **لوحة تحكم (Dashboard)** - Comprehensive business intelligence dashboard

### Technical Features
- **Bilingual Support**: Full Arabic and English language support with RTL text direction
- **Modern GUI**: Professional interface using CustomTkinter with responsive design
- **Database**: SQLite3 with comprehensive schema and relationships
- **Security**: Role-based permissions, secure authentication, audit logging
- **Scalability**: Modular design for easy expansion and maintenance
- **Performance**: Optimized for handling multiple users and large datasets

## 🚀 Installation

### Prerequisites
- Python 3.8 or higher
- Windows 10/11 (primary target), Linux, or macOS

### Step 1: Clone or Download
```bash
git clone <repository-url>
cd account33
```

### Step 2: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 3: Run the Application
```bash
python main.py
```

## 📋 Dependencies

The application requires the following Python packages:

```
customtkinter==5.2.0      # Modern GUI framework
Pillow==10.0.0            # Image processing
matplotlib==3.7.2         # Charts and graphs
pandas==2.0.3             # Data manipulation
reportlab==4.0.4          # PDF generation
openpyxl==3.1.2           # Excel file handling
python-bidi==0.4.2        # Bidirectional text support
arabic-reshaper==3.0.0    # Arabic text reshaping
bcrypt==4.0.1             # Password hashing
```

## 🔐 Default Login Credentials

**Username**: `admin`  
**Password**: `admin123`

> ⚠️ **Important**: Change the default password immediately after first login for security.

## 🏗️ Application Architecture

### Database Schema
The application uses SQLite3 with the following main tables:
- `users` - User accounts and authentication
- `roles` - User roles and permissions
- `branches` - Multi-branch support
- `customers` - Customer management
- `suppliers` - Supplier management
- `products` - Inventory items
- `sales_orders` - Sales transactions
- `purchase_orders` - Purchase transactions
- `invoices` - Invoice management
- `accounts` - Chart of accounts
- `journal_entries` - Accounting entries
- `audit_log` - Activity tracking

### Module Structure
```
├── main.py                 # Application entry point
├── config.py              # Configuration settings
├── database.py            # Database management
├── auth.py                # Authentication system
├── utils.py               # Utility functions
├── gui/                   # GUI components
│   ├── base_window.py     # Base window class
│   ├── components.py      # Reusable components
│   ├── login_window.py    # Login interface
│   └── dashboard.py       # Main dashboard
├── modules/               # Business modules
│   ├── customers.py       # Customer management
│   ├── suppliers.py       # Supplier management
│   ├── sales.py          # Sales management
│   ├── purchasing.py     # Purchase management
│   ├── inventory.py      # Inventory management
│   ├── accounting.py     # Accounting system
│   ├── reports.py        # Report generation
│   ├── invoices.py       # Invoice management
│   ├── employees.py      # Employee management
│   ├── notifications.py  # Notification system
│   ├── audit.py          # Audit logging
│   ├── backup.py         # Backup system
│   ├── settings.py       # System settings
│   └── branches.py       # Branch management
└── languages/            # Language files
    ├── ar.json           # Arabic translations
    └── en.json           # English translations
```

## 🎯 Key Features Demonstration

### Dashboard Metrics
The dashboard displays real-time business metrics:
- **Total Sales**: Current month sales summary
- **Pending Invoices**: Outstanding invoice count
- **Low Stock Items**: Inventory alerts
- **Active Users**: Current system usage

### User Permissions System
Example permission check:
```python
@auth_manager.require_permission('customers', 'create')
def create_customer(self):
    # Only users with customer creation permission can access
    pass
```

### Bilingual Support
The application automatically formats text based on language:
```python
# Arabic text with RTL support
if is_rtl():
    text = format_arabic_text(text)
```

### Audit Trail
All user actions are logged:
```python
self.db.log_activity(
    user_id=auth_manager.get_user_id(),
    action="create_customer",
    table_name="customers",
    record_id=customer_id,
    new_values=data
)
```

## 🔧 Configuration

### System Settings
The application can be configured through:
- `config.py` - Core application settings
- Database settings table - Runtime configuration
- User preference files - Individual user settings

### Language Configuration
To change the default language:
```python
# In config.py
DEFAULT_LANGUAGE = "ar"  # or "en"
```

### Database Configuration
```python
# In config.py
DATABASE_PATH = "erp_database.db"
BACKUP_DIR = "backups"
```

## 📊 Reports and Analytics

The system generates various reports:
- **Financial Reports**: P&L, Balance Sheet, Cash Flow
- **Sales Reports**: Sales analysis, customer reports
- **Inventory Reports**: Stock levels, movement reports
- **User Activity Reports**: System usage analytics

## 🔒 Security Features

- **Password Hashing**: SHA-256 with salt
- **Session Management**: Automatic timeout and refresh
- **Role-Based Access**: Granular permission control
- **Audit Logging**: Complete activity tracking
- **Input Validation**: SQL injection prevention
- **Account Lockout**: Failed login attempt protection

## 🌐 Multi-Language Support

### Adding New Languages
1. Create a new JSON file in `languages/` directory
2. Add language code to `SUPPORTED_LANGUAGES` in config.py
3. Implement language-specific formatting if needed

### RTL Language Support
The application fully supports RTL languages:
- Text direction adjustment
- Layout mirroring
- Arabic text reshaping
- Bidirectional text algorithm

## 🔄 Backup and Restore

### Automatic Backup
- Configurable backup intervals
- Automatic cleanup of old backups
- Backup verification

### Manual Backup
```python
from modules.backup import BackupManager
backup_manager = BackupManager()
backup_manager.create_backup()
```

## 🚀 Performance Optimization

- **Database Indexing**: Optimized queries
- **Lazy Loading**: On-demand data loading
- **Caching**: Frequently accessed data caching
- **Connection Pooling**: Efficient database connections

## 🛠️ Development

### Adding New Modules
1. Create module file in `modules/` directory
2. Inherit from appropriate base classes
3. Implement required permissions
4. Add to navigation menu
5. Update language files

### Custom Components
The GUI framework provides reusable components:
- `FormFrame` - Automatic form generation
- `DataTable` - Sortable, filterable tables
- `SearchFrame` - Search functionality
- `DateEntry` - Date picker widget
- `NumberEntry` - Numeric input validation

## 📝 License

This project is proprietary software. All rights reserved.

## 🤝 Support

For support and questions:
- Check the documentation
- Review the code comments
- Contact the development team

## 🔮 Future Enhancements

Planned features for future versions:
- Web-based interface
- Mobile application
- Advanced reporting with BI tools
- Integration with external systems
- Cloud deployment options
- Advanced workflow management

---

**نظام إدارة الأعمال المتكامل** - Built with ❤️ for modern businesses
