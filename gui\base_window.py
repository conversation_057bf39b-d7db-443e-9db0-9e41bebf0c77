"""
Base window class with common functionality for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from utils import get_text, is_rtl, format_arabic_text, set_language
from config import Config
from auth import auth_manager

class BaseWindow(ctk.CTk):
    """Base window class with common functionality"""
    
    def __init__(self, title=None, width=None, height=None):
        super().__init__()
        
        # Window configuration
        self.title(title or get_text('app_title'))
        self.geometry(f"{width or Config.WINDOW_WIDTH}x{height or Config.WINDOW_HEIGHT}")
        self.minsize(Config.MIN_WIDTH, Config.MIN_HEIGHT)
        
        # Center window on screen
        self.center_window()
        
        # Configure grid
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
        # Setup RTL support
        self.setup_rtl_support()
        
        # Create menu bar
        self.create_menu_bar()
        
        # Bind events
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.bind("<F5>", lambda e: self.refresh_data())
        
        # Status bar
        self.create_status_bar()
    
    def center_window(self):
        """Center window on screen"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_rtl_support(self):
        """Setup RTL support for the window"""
        if is_rtl():
            # Configure RTL layout
            self.option_add('*TCombobox*Listbox.selectBackground', '#0078d4')
    
    def create_menu_bar(self):
        """Create menu bar"""
        self.menubar = tk.Menu(self)
        self.config(menu=self.menubar)
        
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label=get_text('menu.file'), menu=file_menu)
        file_menu.add_command(label=get_text('common.refresh'), command=self.refresh_data, accelerator="F5")
        file_menu.add_separator()
        file_menu.add_command(label=get_text('menu.exit'), command=self.on_closing)
        
        # View menu
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label=get_text('menu.view'), menu=view_menu)
        
        # Language submenu
        language_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label=get_text('common.language'), menu=language_menu)
        language_menu.add_command(label="العربية", command=lambda: self.change_language('ar'))
        language_menu.add_command(label="English", command=lambda: self.change_language('en'))
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label=get_text('menu.help'), menu=help_menu)
        help_menu.add_command(label=get_text('common.about'), command=self.show_about)
        
        # User menu (if authenticated)
        if auth_manager.is_authenticated():
            user_menu = tk.Menu(self.menubar, tearoff=0)
            self.menubar.add_cascade(label=auth_manager.get_user_name(), menu=user_menu)
            user_menu.add_command(label=get_text('common.profile'), command=self.show_profile)
            user_menu.add_command(label=get_text('common.change_password'), command=self.change_password)
            user_menu.add_separator()
            user_menu.add_command(label=get_text('menu.logout'), command=self.logout)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = ctk.CTkFrame(self, height=30)
        self.status_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=(0, 5))
        self.status_frame.grid_propagate(False)
        
        # Status label
        self.status_label = ctk.CTkLabel(self.status_frame, text=get_text('common.ready'))
        self.status_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        # User info (if authenticated)
        if auth_manager.is_authenticated():
            user_info = f"{auth_manager.get_user_name()} - {auth_manager.get_user_role()}"
            if auth_manager.get_user_branch():
                user_info += f" - {auth_manager.get_user_branch()['name']}"
            
            self.user_label = ctk.CTkLabel(self.status_frame, text=user_info)
            self.user_label.grid(row=0, column=1, padx=10, pady=5, sticky="e")
        
        self.status_frame.grid_columnconfigure(0, weight=1)
    
    def set_status(self, message):
        """Set status bar message"""
        if hasattr(self, 'status_label'):
            if is_rtl():
                message = format_arabic_text(message)
            self.status_label.configure(text=message)
    
    def change_language(self, language):
        """Change application language"""
        if set_language(language):
            # Show restart message
            messagebox.showinfo(
                get_text('common.language'),
                get_text('common.restart_required')
            )
    
    def show_about(self):
        """Show about dialog"""
        about_text = f"""
{get_text('app_title')}
{get_text('common.version')}: {Config.VERSION}

{get_text('common.description')}
        """
        
        messagebox.showinfo(get_text('common.about'), about_text)
    
    def show_profile(self):
        """Show user profile dialog"""
        if not auth_manager.is_authenticated():
            return
        
        user = auth_manager.get_current_user()
        profile_window = ProfileWindow(self, user)
        profile_window.grab_set()
    
    def change_password(self):
        """Show change password dialog"""
        if not auth_manager.is_authenticated():
            return
        
        password_window = ChangePasswordWindow(self)
        password_window.grab_set()
    
    def logout(self):
        """Logout user"""
        if messagebox.askyesno(get_text('menu.logout'), get_text('common.confirm_logout')):
            auth_manager.logout()
            self.destroy()
            # Return to login window
            from gui.login_window import LoginWindow
            login_window = LoginWindow()
            login_window.mainloop()
    
    def refresh_data(self):
        """Refresh window data - override in subclasses"""
        self.set_status(get_text('common.refreshing'))
        # Override in subclasses
        self.set_status(get_text('common.ready'))
    
    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno(get_text('common.exit'), get_text('common.confirm_exit')):
            self.destroy()
    
    def show_error(self, message):
        """Show error message"""
        messagebox.showerror(get_text('common.error'), message)
    
    def show_success(self, message):
        """Show success message"""
        messagebox.showinfo(get_text('common.success'), message)
    
    def show_warning(self, message):
        """Show warning message"""
        messagebox.showwarning(get_text('common.warning'), message)
    
    def confirm_action(self, message):
        """Show confirmation dialog"""
        return messagebox.askyesno(get_text('common.confirm'), message)

class ProfileWindow(ctk.CTkToplevel):
    """User profile window"""
    
    def __init__(self, parent, user_data):
        super().__init__(parent)
        self.user_data = user_data
        
        self.title(get_text('common.profile'))
        self.geometry("400x300")
        self.resizable(False, False)
        
        # Center on parent
        self.transient(parent)
        self.grab_set()
        
        self.setup_widgets()
    
    def setup_widgets(self):
        """Setup profile widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Profile info
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=(0, 20))
        
        # User info labels
        labels = [
            (get_text('common.username'), self.user_data.get('username', '')),
            (get_text('common.full_name'), self.user_data.get('full_name', '')),
            (get_text('common.email'), self.user_data.get('email', '')),
            (get_text('common.role'), self.user_data.get('role_name', '')),
            (get_text('common.branch'), self.user_data.get('branch_name', '')),
            (get_text('common.last_login'), self.user_data.get('last_login', ''))
        ]
        
        for i, (label_text, value) in enumerate(labels):
            label = ctk.CTkLabel(info_frame, text=f"{label_text}:")
            label.grid(row=i, column=0, sticky="w", padx=10, pady=5)
            
            value_label = ctk.CTkLabel(info_frame, text=str(value))
            value_label.grid(row=i, column=1, sticky="w", padx=10, pady=5)
        
        # Close button
        close_button = ctk.CTkButton(main_frame, text=get_text('common.close'),
                                   command=self.destroy)
        close_button.pack(pady=10)

class ChangePasswordWindow(ctk.CTkToplevel):
    """Change password window"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title(get_text('common.change_password'))
        self.geometry("400x250")
        self.resizable(False, False)
        
        # Center on parent
        self.transient(parent)
        self.grab_set()
        
        self.setup_widgets()
    
    def setup_widgets(self):
        """Setup password change widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Form frame
        form_frame = ctk.CTkFrame(main_frame)
        form_frame.pack(fill="x", pady=(0, 20))
        
        # Old password
        old_pass_label = ctk.CTkLabel(form_frame, text=get_text('common.old_password'))
        old_pass_label.grid(row=0, column=0, sticky="w", padx=10, pady=5)
        
        self.old_password_entry = ctk.CTkEntry(form_frame, show="*", width=200)
        self.old_password_entry.grid(row=0, column=1, padx=10, pady=5)
        
        # New password
        new_pass_label = ctk.CTkLabel(form_frame, text=get_text('common.new_password'))
        new_pass_label.grid(row=1, column=0, sticky="w", padx=10, pady=5)
        
        self.new_password_entry = ctk.CTkEntry(form_frame, show="*", width=200)
        self.new_password_entry.grid(row=1, column=1, padx=10, pady=5)
        
        # Confirm password
        confirm_pass_label = ctk.CTkLabel(form_frame, text=get_text('common.confirm_password'))
        confirm_pass_label.grid(row=2, column=0, sticky="w", padx=10, pady=5)
        
        self.confirm_password_entry = ctk.CTkEntry(form_frame, show="*", width=200)
        self.confirm_password_entry.grid(row=2, column=1, padx=10, pady=5)
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x")
        
        # Change button
        change_button = ctk.CTkButton(buttons_frame, text=get_text('common.change'),
                                    command=self.change_password)
        change_button.pack(side="left", padx=10, pady=10)
        
        # Cancel button
        cancel_button = ctk.CTkButton(buttons_frame, text=get_text('common.cancel'),
                                    command=self.destroy)
        cancel_button.pack(side="right", padx=10, pady=10)
    
    def change_password(self):
        """Change user password"""
        old_password = self.old_password_entry.get()
        new_password = self.new_password_entry.get()
        confirm_password = self.confirm_password_entry.get()
        
        # Validate inputs
        if not old_password or not new_password or not confirm_password:
            messagebox.showerror(get_text('common.error'), 
                               get_text('forms.required_field'))
            return
        
        if new_password != confirm_password:
            messagebox.showerror(get_text('common.error'), 
                               get_text('forms.passwords_dont_match'))
            return
        
        if len(new_password) < Config.PASSWORD_MIN_LENGTH:
            messagebox.showerror(get_text('common.error'), 
                               get_text('forms.password_too_short'))
            return
        
        # Change password
        success, message = auth_manager.change_password(old_password, new_password)
        
        if success:
            messagebox.showinfo(get_text('common.success'), message)
            self.destroy()
        else:
            messagebox.showerror(get_text('common.error'), message)
