"""
Login window for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk
from utils import get_text, is_rtl, format_arabic_text
from config import Config
from auth import auth_manager

# تحسين الألوان والتصميم
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

# ألوان مخصصة لنافذة تسجيل الدخول
LOGIN_COLORS = {
    'primary': '#1f538d',
    'secondary': '#14a085',
    'accent': '#f39c12',
    'success': '#27ae60',
    'white': '#ffffff',
    'light': '#f8f9fa',
    'dark': '#2c3e50',
    'text_primary': '#2c3e50',
    'text_secondary': '#6c757d',
    'gradient_start': '#667eea',
    'gradient_end': '#764ba2',
    'shadow': '#00000015'
}

class LoginWindow(ctk.CTk):
    """Professional login window with modern design"""

    def __init__(self):
        super().__init__()

        # Window configuration
        self.title(get_text('login.title'))
        self.geometry("600x500")  # حجم أكبر للتصميم الاحترافي
        self.resizable(False, False)

        # تطبيق التصميم الاحترافي
        self.configure(fg_color=LOGIN_COLORS['light'])

        # Center window
        self.center_window()

        # إعداد الخطوط
        self.setup_fonts()

        # Variables
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()

        # Setup widgets
        self.setup_widgets()

        # Bind events
        self.bind('<Return>', lambda e: self.login())
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Focus on username entry
        self.username_entry.focus()

    def setup_fonts(self):
        """إعداد الخطوط الاحترافية"""
        self.fonts = {
            'title': ctk.CTkFont(size=28, weight="bold"),
            'subtitle': ctk.CTkFont(size=16),
            'label': ctk.CTkFont(size=14, weight="bold"),
            'body': ctk.CTkFont(size=14),
            'small': ctk.CTkFont(size=12),
            'button': ctk.CTkFont(size=16, weight="bold")
        }

    def center_window(self):
        """Center window on screen"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")

    def setup_widgets(self):
        """Setup professional login widgets"""
        # Background container with gradient effect
        bg_frame = ctk.CTkFrame(
            self,
            fg_color=LOGIN_COLORS['white'],
            corner_radius=0
        )
        bg_frame.pack(fill="both", expand=True)

        # Main login card with shadow effect
        main_card = ctk.CTkFrame(
            bg_frame,
            fg_color=LOGIN_COLORS['white'],
            corner_radius=25,
            border_width=2,
            border_color=LOGIN_COLORS['primary']
        )
        main_card.pack(expand=True, padx=50, pady=50)

        # Header section with logo and title
        self.create_header_section(main_card)

        # Login form section
        self.create_form_section(main_card)

        # Footer section
        self.create_footer_section(main_card)

    def create_header_section(self, parent):
        """Create professional header section"""
        header_frame = ctk.CTkFrame(parent, fg_color="transparent")
        header_frame.pack(fill="x", pady=(30, 20))

        # Logo/Icon (using emoji for now)
        logo_label = ctk.CTkLabel(
            header_frame,
            text="🏢",
            font=ctk.CTkFont(size=48)
        )
        logo_label.pack(pady=(0, 10))

        # App title with modern styling
        title_text = "نظام إدارة الأعمال"
        if is_rtl():
            title_text = format_arabic_text(title_text)

        title_label = ctk.CTkLabel(
            header_frame,
            text=title_text,
            font=self.fonts['title'],
            text_color=LOGIN_COLORS['primary']
        )
        title_label.pack()

        # Subtitle
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="Integrated Business Management System",
            font=self.fonts['subtitle'],
            text_color=LOGIN_COLORS['text_secondary']
        )
        subtitle_label.pack(pady=(5, 0))

        # Welcome message
        welcome_label = ctk.CTkLabel(
            header_frame,
            text="👋 مرحباً بك - Welcome",
            font=self.fonts['body'],
            text_color=LOGIN_COLORS['text_primary']
        )
        welcome_label.pack(pady=(10, 0))

    def create_form_section(self, parent):
        """Create modern form section"""

        form_frame = ctk.CTkFrame(parent, fg_color="transparent")
        form_frame.pack(fill="x", padx=40, pady=20)

        # Username field with icon
        username_container = ctk.CTkFrame(form_frame, fg_color="transparent")
        username_container.pack(fill="x", pady=(0, 15))

        username_label = ctk.CTkLabel(
            username_container,
            text=f"👤 {get_text('login.username')}",
            font=self.fonts['label'],
            text_color=LOGIN_COLORS['text_primary']
        )
        username_label.pack(anchor="w" if not is_rtl() else "e", pady=(0, 8))

        self.username_entry = ctk.CTkEntry(
            username_container,
            textvariable=self.username_var,
            placeholder_text=get_text('login.username'),
            height=45,
            font=self.fonts['body'],
            corner_radius=12,
            border_width=2,
            border_color=LOGIN_COLORS['primary']
        )
        self.username_entry.pack(fill="x")

        # Password field with icon
        password_container = ctk.CTkFrame(form_frame, fg_color="transparent")
        password_container.pack(fill="x", pady=(0, 15))

        password_label = ctk.CTkLabel(
            password_container,
            text=f"🔒 {get_text('login.password')}",
            font=self.fonts['label'],
            text_color=LOGIN_COLORS['text_primary']
        )
        password_label.pack(anchor="w" if not is_rtl() else "e", pady=(0, 8))

        self.password_entry = ctk.CTkEntry(
            password_container,
            textvariable=self.password_var,
            placeholder_text=get_text('login.password'),
            show="*",
            height=45,
            font=self.fonts['body'],
            corner_radius=12,
            border_width=2,
            border_color=LOGIN_COLORS['primary']
        )
        self.password_entry.pack(fill="x")

        # Options container
        options_frame = ctk.CTkFrame(form_frame, fg_color="transparent")
        options_frame.pack(fill="x", pady=(10, 20))

        # Remember me checkbox
        self.remember_checkbox = ctk.CTkCheckBox(
            options_frame,
            text=f"💾 {get_text('login.remember_me')}",
            variable=self.remember_var,
            font=self.fonts['small'],
            text_color=LOGIN_COLORS['text_secondary']
        )
        self.remember_checkbox.pack(side="left")

        # Forgot password link (placeholder)
        forgot_button = ctk.CTkButton(
            options_frame,
            text="🤔 نسيت كلمة المرور؟",
            font=self.fonts['small'],
            fg_color="transparent",
            text_color=LOGIN_COLORS['primary'],
            hover_color=LOGIN_COLORS['light'],
            width=150,
            height=25,
            command=self.show_forgot_password
        )
        forgot_button.pack(side="right")

        # Login button with gradient effect
        self.login_button = ctk.CTkButton(
            form_frame,
            text=f"🚀 {get_text('login.login_button')}",
            command=self.login,
            height=50,
            font=self.fonts['button'],
            fg_color=LOGIN_COLORS['primary'],
            hover_color=LOGIN_COLORS['secondary'],
            corner_radius=15
        )
        self.login_button.pack(fill="x", pady=(0, 10))

    def create_footer_section(self, parent):
        """Create footer section"""

        footer_frame = ctk.CTkFrame(parent, fg_color="transparent")
        footer_frame.pack(fill="x", padx=40, pady=(10, 30))

        # Language selection with modern design
        lang_container = ctk.CTkFrame(
            footer_frame,
            fg_color=LOGIN_COLORS['light'],
            corner_radius=15
        )
        lang_container.pack(fill="x", pady=(0, 15))

        lang_title = ctk.CTkLabel(
            lang_container,
            text="🌍 Language / اللغة",
            font=self.fonts['small'],
            text_color=LOGIN_COLORS['text_primary']
        )
        lang_title.pack(pady=(10, 5))

        # Language buttons container
        lang_buttons_frame = ctk.CTkFrame(lang_container, fg_color="transparent")
        lang_buttons_frame.pack(pady=(0, 10))

        # Arabic button
        ar_button = ctk.CTkButton(
            lang_buttons_frame,
            text="🇸🇦 العربية",
            width=100,
            height=35,
            font=self.fonts['small'],
            fg_color=LOGIN_COLORS['success'],
            hover_color=LOGIN_COLORS['secondary'],
            corner_radius=10,
            command=lambda: self.change_language('ar')
        )
        ar_button.pack(side="left", padx=5)

        # English button
        en_button = ctk.CTkButton(
            lang_buttons_frame,
            text="🇺🇸 English",
            width=100,
            height=35,
            font=self.fonts['small'],
            fg_color=LOGIN_COLORS['accent'],
            hover_color=LOGIN_COLORS['secondary'],
            corner_radius=10,
            command=lambda: self.change_language('en')
        )
        en_button.pack(side="right", padx=5)

        # Version and copyright info
        info_frame = ctk.CTkFrame(footer_frame, fg_color="transparent")
        info_frame.pack(fill="x")

        version_label = ctk.CTkLabel(
            info_frame,
            text=f"💻 Version {Config.VERSION} | © 2024 ERP System",
            font=self.fonts['small'],
            text_color=LOGIN_COLORS['text_secondary']
        )
        version_label.pack()

    def show_forgot_password(self):
        """Show forgot password dialog"""
        forgot_window = ForgotPasswordWindow(self)
        forgot_window.grab_set()

        # Load saved credentials
        self.load_saved_credentials()

    def login(self):
        """Perform login"""
        username = self.username_var.get().strip()
        password = self.password_var.get()

        # Validate inputs
        if not username or not password:
            self.show_error(get_text('forms.required_field'))
            return

        # Disable login button during authentication
        self.login_button.configure(state="disabled", text=get_text('common.loading'))
        self.update()

        try:
            # Attempt authentication
            if auth_manager.login(username, password):
                # Save credentials if remember me is checked
                if self.remember_var.get():
                    self.save_credentials(username)
                else:
                    self.clear_saved_credentials()

                # Show welcome message
                user_name = auth_manager.get_user_name()
                welcome_msg = f"{get_text('login.welcome')} {user_name}"

                # Close login window and open main application
                self.destroy()
                self.open_main_application()

            else:
                self.show_error(get_text('login.invalid_credentials'))

        except Exception as e:
            self.show_error(f"Login error: {str(e)}")

        finally:
            # Re-enable login button
            self.login_button.configure(state="normal", text=get_text('login.login_button'))

    def open_main_application(self):
        """Open main application window"""
        from gui.dashboard import DashboardWindow
        dashboard = DashboardWindow()
        dashboard.mainloop()

    def change_language(self, language):
        """Change application language"""
        from utils import set_language
        if set_language(language):
            # Recreate window with new language
            self.destroy()
            new_login = LoginWindow()
            new_login.mainloop()

    def save_credentials(self, username):
        """Save login credentials"""
        try:
            import json
            credentials = {
                'username': username,
                'remember': True
            }
            with open('login_credentials.json', 'w') as f:
                json.dump(credentials, f)
        except:
            pass  # Ignore errors

    def load_saved_credentials(self):
        """Load saved login credentials"""
        try:
            import json
            import os
            if os.path.exists('login_credentials.json'):
                with open('login_credentials.json', 'r') as f:
                    credentials = json.load(f)
                    if credentials.get('remember'):
                        self.username_var.set(credentials.get('username', ''))
                        self.remember_var.set(True)
                        # Focus on password field if username is loaded
                        if self.username_var.get():
                            self.password_entry.focus()
        except:
            pass  # Ignore errors

    def clear_saved_credentials(self):
        """Clear saved credentials"""
        try:
            import os
            if os.path.exists('login_credentials.json'):
                os.remove('login_credentials.json')
        except:
            pass  # Ignore errors

    def show_error(self, message):
        """Show error message"""
        if is_rtl():
            message = format_arabic_text(message)
        messagebox.showerror(get_text('common.error'), message)

    def on_closing(self):
        """Handle window closing"""
        self.destroy()

class ForgotPasswordWindow(ctk.CTkToplevel):
    """Forgot password window"""

    def __init__(self, parent):
        super().__init__(parent)

        self.title(get_text('login.forgot_password'))
        self.geometry("400x300")
        self.resizable(False, False)

        # Center on parent
        self.transient(parent)
        self.grab_set()

        self.setup_widgets()

    def setup_widgets(self):
        """Setup forgot password widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=get_text('login.forgot_password'),
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # Instructions
        instructions = ctk.CTkLabel(
            main_frame,
            text=get_text('login.forgot_password_instructions'),
            wraplength=350
        )
        instructions.pack(pady=(0, 20))

        # Email entry
        self.email_var = tk.StringVar()
        email_label = ctk.CTkLabel(main_frame, text=get_text('common.email'))
        email_label.pack(anchor="w", padx=20)

        self.email_entry = ctk.CTkEntry(
            main_frame,
            textvariable=self.email_var,
            placeholder_text=get_text('common.email'),
            width=300
        )
        self.email_entry.pack(pady=(5, 20))

        # Buttons frame
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20)

        # Send button
        send_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.send'),
            command=self.send_reset_email
        )
        send_button.pack(side="left", padx=(0, 10))

        # Cancel button
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.cancel'),
            command=self.destroy
        )
        cancel_button.pack(side="right")

    def send_reset_email(self):
        """Send password reset email"""
        email = self.email_var.get().strip()

        if not email:
            messagebox.showerror(get_text('common.error'),
                               get_text('forms.required_field'))
            return

        # TODO: Implement password reset functionality
        messagebox.showinfo(
            get_text('common.success'),
            get_text('login.reset_email_sent')
        )
        self.destroy()

if __name__ == "__main__":
    # Run login window
    login = LoginWindow()
    login.mainloop()
