"""
Login window for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk
from utils import get_text, is_rtl, format_arabic_text
from config import Config
from auth import auth_manager

class LoginWindow(ctk.CTk):
    """Login window with authentication"""
    
    def __init__(self):
        super().__init__()
        
        # Window configuration
        self.title(get_text('login.title'))
        self.geometry("500x400")
        self.resizable(False, False)
        
        # Center window
        self.center_window()
        
        # Configure appearance
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # Variables
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
        
        # Setup widgets
        self.setup_widgets()
        
        # Bind events
        self.bind('<Return>', lambda e: self.login())
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Focus on username entry
        self.username_entry.focus()
    
    def center_window(self):
        """Center window on screen"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_widgets(self):
        """Setup login widgets"""
        # Main container
        main_frame = ctk.CTkFrame(self, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=40, pady=40)
        
        # Header frame
        header_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=(20, 30))
        
        # App title
        title_text = get_text('app_title')
        if is_rtl():
            title_text = format_arabic_text(title_text)
        
        title_label = ctk.CTkLabel(
            header_frame,
            text=title_text,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack()
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text=get_text('login.title'),
            font=ctk.CTkFont(size=16)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Login form frame
        form_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        form_frame.pack(fill="x", padx=40, pady=20)
        
        # Username field
        username_label = ctk.CTkLabel(form_frame, text=get_text('login.username'))
        username_label.pack(anchor="w" if not is_rtl() else "e", pady=(0, 5))
        
        self.username_entry = ctk.CTkEntry(
            form_frame,
            textvariable=self.username_var,
            placeholder_text=get_text('login.username'),
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.username_entry.pack(fill="x", pady=(0, 15))
        
        # Password field
        password_label = ctk.CTkLabel(form_frame, text=get_text('login.password'))
        password_label.pack(anchor="w" if not is_rtl() else "e", pady=(0, 5))
        
        self.password_entry = ctk.CTkEntry(
            form_frame,
            textvariable=self.password_var,
            placeholder_text=get_text('login.password'),
            show="*",
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.password_entry.pack(fill="x", pady=(0, 15))
        
        # Remember me checkbox
        self.remember_checkbox = ctk.CTkCheckBox(
            form_frame,
            text=get_text('login.remember_me'),
            variable=self.remember_var
        )
        self.remember_checkbox.pack(anchor="w" if not is_rtl() else "e", pady=(0, 20))
        
        # Login button
        self.login_button = ctk.CTkButton(
            form_frame,
            text=get_text('login.login_button'),
            command=self.login,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.login_button.pack(fill="x", pady=(0, 15))
        
        # Language selection frame
        lang_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        lang_frame.pack(fill="x", padx=40, pady=(0, 20))
        
        lang_label = ctk.CTkLabel(lang_frame, text="Language / اللغة:")
        lang_label.pack(side="left")
        
        # Language buttons
        ar_button = ctk.CTkButton(
            lang_frame,
            text="العربية",
            width=80,
            height=30,
            command=lambda: self.change_language('ar')
        )
        ar_button.pack(side="right", padx=(5, 0))
        
        en_button = ctk.CTkButton(
            lang_frame,
            text="English",
            width=80,
            height=30,
            command=lambda: self.change_language('en')
        )
        en_button.pack(side="right", padx=(5, 0))
        
        # Footer
        footer_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        footer_frame.pack(fill="x", pady=(0, 20))
        
        version_label = ctk.CTkLabel(
            footer_frame,
            text=f"Version {Config.VERSION}",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        version_label.pack()
        
        # Load saved credentials
        self.load_saved_credentials()
    
    def login(self):
        """Perform login"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        # Validate inputs
        if not username or not password:
            self.show_error(get_text('forms.required_field'))
            return
        
        # Disable login button during authentication
        self.login_button.configure(state="disabled", text=get_text('common.loading'))
        self.update()
        
        try:
            # Attempt authentication
            if auth_manager.login(username, password):
                # Save credentials if remember me is checked
                if self.remember_var.get():
                    self.save_credentials(username)
                else:
                    self.clear_saved_credentials()
                
                # Show welcome message
                user_name = auth_manager.get_user_name()
                welcome_msg = f"{get_text('login.welcome')} {user_name}"
                
                # Close login window and open main application
                self.destroy()
                self.open_main_application()
                
            else:
                self.show_error(get_text('login.invalid_credentials'))
        
        except Exception as e:
            self.show_error(f"Login error: {str(e)}")
        
        finally:
            # Re-enable login button
            self.login_button.configure(state="normal", text=get_text('login.login_button'))
    
    def open_main_application(self):
        """Open main application window"""
        from gui.dashboard import DashboardWindow
        dashboard = DashboardWindow()
        dashboard.mainloop()
    
    def change_language(self, language):
        """Change application language"""
        from utils import set_language
        if set_language(language):
            # Recreate window with new language
            self.destroy()
            new_login = LoginWindow()
            new_login.mainloop()
    
    def save_credentials(self, username):
        """Save login credentials"""
        try:
            import json
            credentials = {
                'username': username,
                'remember': True
            }
            with open('login_credentials.json', 'w') as f:
                json.dump(credentials, f)
        except:
            pass  # Ignore errors
    
    def load_saved_credentials(self):
        """Load saved login credentials"""
        try:
            import json
            import os
            if os.path.exists('login_credentials.json'):
                with open('login_credentials.json', 'r') as f:
                    credentials = json.load(f)
                    if credentials.get('remember'):
                        self.username_var.set(credentials.get('username', ''))
                        self.remember_var.set(True)
                        # Focus on password field if username is loaded
                        if self.username_var.get():
                            self.password_entry.focus()
        except:
            pass  # Ignore errors
    
    def clear_saved_credentials(self):
        """Clear saved credentials"""
        try:
            import os
            if os.path.exists('login_credentials.json'):
                os.remove('login_credentials.json')
        except:
            pass  # Ignore errors
    
    def show_error(self, message):
        """Show error message"""
        if is_rtl():
            message = format_arabic_text(message)
        messagebox.showerror(get_text('common.error'), message)
    
    def on_closing(self):
        """Handle window closing"""
        self.destroy()

class ForgotPasswordWindow(ctk.CTkToplevel):
    """Forgot password window"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title(get_text('login.forgot_password'))
        self.geometry("400x300")
        self.resizable(False, False)
        
        # Center on parent
        self.transient(parent)
        self.grab_set()
        
        self.setup_widgets()
    
    def setup_widgets(self):
        """Setup forgot password widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=get_text('login.forgot_password'),
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # Instructions
        instructions = ctk.CTkLabel(
            main_frame,
            text=get_text('login.forgot_password_instructions'),
            wraplength=350
        )
        instructions.pack(pady=(0, 20))
        
        # Email entry
        self.email_var = tk.StringVar()
        email_label = ctk.CTkLabel(main_frame, text=get_text('common.email'))
        email_label.pack(anchor="w", padx=20)
        
        self.email_entry = ctk.CTkEntry(
            main_frame,
            textvariable=self.email_var,
            placeholder_text=get_text('common.email'),
            width=300
        )
        self.email_entry.pack(pady=(5, 20))
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20)
        
        # Send button
        send_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.send'),
            command=self.send_reset_email
        )
        send_button.pack(side="left", padx=(0, 10))
        
        # Cancel button
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.cancel'),
            command=self.destroy
        )
        cancel_button.pack(side="right")
    
    def send_reset_email(self):
        """Send password reset email"""
        email = self.email_var.get().strip()
        
        if not email:
            messagebox.showerror(get_text('common.error'), 
                               get_text('forms.required_field'))
            return
        
        # TODO: Implement password reset functionality
        messagebox.showinfo(
            get_text('common.success'),
            get_text('login.reset_email_sent')
        )
        self.destroy()

if __name__ == "__main__":
    # Run login window
    login = LoginWindow()
    login.mainloop()
