"""
Inventory Management Module for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from gui.base_window import BaseWindow
from gui.components import FormFrame, DataTable, SearchFrame, show_message, show_confirmation
from utils import get_text, ValidationUtils, NumberUtils
from auth import auth_manager
from database import DatabaseManager

class InventoryWindow(ctk.CTkToplevel):
    """Inventory management window"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title(get_text('modules.inventory'))
        self.geometry("1400x900")
        
        self.db = DatabaseManager()
        self.selected_product = None
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
        self.load_products()
        self.load_categories()
    
    def setup_widgets(self):
        """Setup inventory management widgets"""
        # Main container
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=get_text('modules.inventory'),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=20)
        
        # Left panel - Product form
        self.create_product_form(main_frame)
        
        # Right panel - Product list
        self.create_product_list(main_frame)
    
    def create_product_form(self, parent):
        """Create product form"""
        form_frame = ctk.CTkFrame(parent)
        form_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 5), pady=5)
        form_frame.grid_rowconfigure(1, weight=1)
        
        # Form title
        form_title = ctk.CTkLabel(
            form_frame,
            text=get_text('common.product_details'),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        form_title.grid(row=0, column=0, pady=15)
        
        # Product form
        self.product_form = FormFrame(form_frame)
        self.product_form.grid(row=1, column=0, sticky="nsew", padx=20, pady=10)
        
        # Add form fields
        self.product_form.add_field(
            'code', get_text('common.product_code'), 
            required=True, width=250
        )
        
        self.product_form.add_field(
            'name', get_text('common.name'), 
            required=True, width=250
        )
        
        self.product_form.add_field(
            'description', get_text('common.description'), 
            field_type="text", width=250
        )
        
        # Category combobox will be populated later
        self.product_form.add_field(
            'category_id', get_text('common.category'), 
            field_type="combobox", width=250
        )
        
        self.product_form.add_field(
            'unit_of_measure', get_text('common.unit'), 
            width=250
        )
        
        self.product_form.add_field(
            'cost_price', get_text('common.cost_price'), 
            field_type="number", width=250
        )
        
        self.product_form.add_field(
            'selling_price', get_text('common.selling_price'), 
            field_type="number", width=250
        )
        
        self.product_form.add_field(
            'current_stock', get_text('common.current_stock'), 
            field_type="number", width=250
        )
        
        self.product_form.add_field(
            'minimum_stock', get_text('common.minimum_stock'), 
            field_type="number", width=250
        )
        
        self.product_form.add_field(
            'maximum_stock', get_text('common.maximum_stock'), 
            field_type="number", width=250
        )
        
        self.product_form.add_field(
            'reorder_point', get_text('common.reorder_point'), 
            field_type="number", width=250
        )
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(form_frame)
        buttons_frame.grid(row=2, column=0, sticky="ew", padx=20, pady=20)
        
        # Action buttons
        self.save_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.save'),
            command=self.save_product,
            width=100
        )
        self.save_button.pack(side="left", padx=5)
        
        self.update_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.update'),
            command=self.update_product,
            width=100,
            state="disabled"
        )
        self.update_button.pack(side="left", padx=5)
        
        self.delete_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.delete'),
            command=self.delete_product,
            width=100,
            state="disabled",
            fg_color="red"
        )
        self.delete_button.pack(side="left", padx=5)
        
        self.clear_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.clear'),
            command=self.clear_form,
            width=100
        )
        self.clear_button.pack(side="left", padx=5)
        
        # Stock adjustment button
        self.adjust_stock_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.adjust_stock'),
            command=self.adjust_stock,
            width=120,
            state="disabled"
        )
        self.adjust_stock_button.pack(side="left", padx=5)
    
    def create_product_list(self, parent):
        """Create product list"""
        list_frame = ctk.CTkFrame(parent)
        list_frame.grid(row=1, column=1, sticky="nsew", padx=(5, 0), pady=5)
        list_frame.grid_rowconfigure(2, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # List title
        list_title = ctk.CTkLabel(
            list_frame,
            text=get_text('common.product_list'),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        list_title.grid(row=0, column=0, pady=15)
        
        # Search frame
        self.search_frame = SearchFrame(list_frame, on_search=self.search_products)
        self.search_frame.grid(row=1, column=0, sticky="ew", padx=20, pady=10)
        
        # Product table
        columns = {
            'id': {'text': 'ID', 'width': 50},
            'code': {'text': get_text('common.code'), 'width': 100},
            'name': {'text': get_text('common.name'), 'width': 200},
            'category': {'text': get_text('common.category'), 'width': 120},
            'unit_of_measure': {'text': get_text('common.unit'), 'width': 80},
            'cost_price': {'text': get_text('common.cost'), 'width': 100, 'format': 'currency'},
            'selling_price': {'text': get_text('common.price'), 'width': 100, 'format': 'currency'},
            'current_stock': {'text': get_text('common.stock'), 'width': 80, 'format': 'number'},
            'minimum_stock': {'text': get_text('common.min'), 'width': 80, 'format': 'number'},
            'status': {'text': get_text('common.status'), 'width': 100}
        }
        
        self.product_table = DataTable(list_frame, columns)
        self.product_table.grid(row=2, column=0, sticky="nsew", padx=20, pady=10)
        
        # Bind table events
        self.product_table.tree.bind('<ButtonRelease-1>', self.on_product_select)
        self.product_table.tree.bind('<Double-1>', self.on_product_double_click)
    
    def load_categories(self):
        """Load product categories"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT id, name FROM categories WHERE is_active = 1 ORDER BY name
            ''')
            
            categories = cursor.fetchall()
            conn.close()
            
            # Update category combobox
            category_field = self.product_form.fields['category_id']['widget']
            category_values = [f"{cat['id']} - {cat['name']}" for cat in categories]
            category_field.configure(values=category_values)
            
        except Exception as e:
            print(f"Error loading categories: {e}")
    
    def load_products(self):
        """Load products from database"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get branch filter
            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE p.branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                {branch_filter}
                ORDER BY p.name
            ''', params)
            
            products = []
            for row in cursor.fetchall():
                product = dict(row)
                
                # Determine stock status
                if product['current_stock'] <= 0:
                    product['status'] = get_text('common.out_of_stock')
                elif product['current_stock'] <= product['minimum_stock']:
                    product['status'] = get_text('common.low_stock')
                elif product['current_stock'] >= product['maximum_stock']:
                    product['status'] = get_text('common.overstock')
                else:
                    product['status'] = get_text('common.normal')
                
                product['category'] = product['category_name'] or ''
                products.append(product)
            
            self.product_table.load_data(products)
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading products: {str(e)}", "error")
    
    def search_products(self, search_term):
        """Search products"""
        if not search_term:
            self.load_products()
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get branch filter
            branch_filter = ""
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
            if auth_manager.get_user_branch():
                branch_filter = "AND p.branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE (p.code LIKE ? OR p.name LIKE ? OR p.description LIKE ?)
                {branch_filter}
                ORDER BY p.name
            ''', params)
            
            products = []
            for row in cursor.fetchall():
                product = dict(row)
                
                # Determine stock status
                if product['current_stock'] <= 0:
                    product['status'] = get_text('common.out_of_stock')
                elif product['current_stock'] <= product['minimum_stock']:
                    product['status'] = get_text('common.low_stock')
                elif product['current_stock'] >= product['maximum_stock']:
                    product['status'] = get_text('common.overstock')
                else:
                    product['status'] = get_text('common.normal')
                
                product['category'] = product['category_name'] or ''
                products.append(product)
            
            self.product_table.load_data(products)
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error searching products: {str(e)}", "error")
    
    def on_product_select(self, event):
        """Handle product selection"""
        selected_product = self.product_table.get_selected_item()
        if selected_product:
            self.selected_product = selected_product
            self.load_product_details(selected_product['id'])
            
            # Enable update, delete, and stock adjustment buttons
            self.update_button.configure(state="normal")
            self.delete_button.configure(state="normal")
            self.adjust_stock_button.configure(state="normal")
    
    def on_product_double_click(self, event):
        """Handle product double click"""
        selected_product = self.product_table.get_selected_item()
        if selected_product:
            self.show_product_details(selected_product['id'])
    
    def load_product_details(self, product_id):
        """Load product details into form"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM products WHERE id = ?
            ''', (product_id,))
            
            product = cursor.fetchone()
            conn.close()
            
            if product:
                # Populate form
                self.product_form.set_value('code', product['code'])
                self.product_form.set_value('name', product['name'])
                self.product_form.set_value('description', product['description'])
                
                # Set category
                if product['category_id']:
                    category_field = self.product_form.fields['category_id']['widget']
                    for value in category_field.cget('values'):
                        if value.startswith(str(product['category_id'])):
                            category_field.set(value)
                            break
                
                self.product_form.set_value('unit_of_measure', product['unit_of_measure'])
                self.product_form.set_value('cost_price', product['cost_price'])
                self.product_form.set_value('selling_price', product['selling_price'])
                self.product_form.set_value('current_stock', product['current_stock'])
                self.product_form.set_value('minimum_stock', product['minimum_stock'])
                self.product_form.set_value('maximum_stock', product['maximum_stock'])
                self.product_form.set_value('reorder_point', product['reorder_point'])
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading product details: {str(e)}", "error")
    
    def save_product(self):
        """Save new product"""
        # Check permissions
        if not auth_manager.has_permission('products', 'create'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Validate form
        errors = self.product_form.validate_form()
        if errors:
            show_message(get_text('common.error'), '\n'.join(errors), "error")
            return
        
        try:
            # Get form data
            data = self.product_form.get_form_data()
            
            # Extract category ID
            category_id = None
            if data['category_id']:
                category_id = int(data['category_id'].split(' - ')[0])
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if product code already exists
            cursor.execute('''
                SELECT id FROM products WHERE code = ? AND branch_id = ?
            ''', (data['code'], auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None))
            
            if cursor.fetchone():
                show_message(get_text('common.error'), get_text('error.product_code_exists'), "error")
                conn.close()
                return
            
            # Insert product
            cursor.execute('''
                INSERT INTO products (
                    code, name, description, category_id, unit_of_measure,
                    cost_price, selling_price, current_stock, minimum_stock,
                    maximum_stock, reorder_point, branch_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                data['code'], data['name'], data['description'], category_id,
                data['unit_of_measure'] or 'قطعة', data['cost_price'] or 0,
                data['selling_price'] or 0, data['current_stock'] or 0,
                data['minimum_stock'] or 0, data['maximum_stock'] or 0,
                data['reorder_point'] or 0,
                auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None
            ))
            
            product_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="create_product",
                table_name="products",
                record_id=product_id,
                new_values=data
            )
            
            show_message(get_text('common.success'), get_text('success.product_saved'), "success")
            self.clear_form()
            self.load_products()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error saving product: {str(e)}", "error")
    
    def update_product(self):
        """Update selected product"""
        if not self.selected_product:
            return
        
        # Check permissions
        if not auth_manager.has_permission('products', 'update'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Validate form
        errors = self.product_form.validate_form()
        if errors:
            show_message(get_text('common.error'), '\n'.join(errors), "error")
            return
        
        try:
            # Get form data
            data = self.product_form.get_form_data()
            
            # Extract category ID
            category_id = None
            if data['category_id']:
                category_id = int(data['category_id'].split(' - ')[0])
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get old values for audit
            cursor.execute('SELECT * FROM products WHERE id = ?', (self.selected_product['id'],))
            old_values = dict(cursor.fetchone())
            
            # Update product
            cursor.execute('''
                UPDATE products SET
                    code = ?, name = ?, description = ?, category_id = ?,
                    unit_of_measure = ?, cost_price = ?, selling_price = ?,
                    minimum_stock = ?, maximum_stock = ?, reorder_point = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                data['code'], data['name'], data['description'], category_id,
                data['unit_of_measure'] or 'قطعة', data['cost_price'] or 0,
                data['selling_price'] or 0, data['minimum_stock'] or 0,
                data['maximum_stock'] or 0, data['reorder_point'] or 0,
                self.selected_product['id']
            ))
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="update_product",
                table_name="products",
                record_id=self.selected_product['id'],
                old_values=old_values,
                new_values=data
            )
            
            show_message(get_text('common.success'), get_text('success.product_updated'), "success")
            self.load_products()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error updating product: {str(e)}", "error")
    
    def delete_product(self):
        """Delete selected product"""
        if not self.selected_product:
            return
        
        # Check permissions
        if not auth_manager.has_permission('products', 'delete'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Confirm deletion
        if not show_confirmation(
            get_text('common.confirm'),
            f"{get_text('common.confirm_delete')} {self.selected_product['name']}?"
        ):
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if product has transactions
            cursor.execute('''
                SELECT COUNT(*) as count FROM sales_order_items WHERE product_id = ?
                UNION ALL
                SELECT COUNT(*) as count FROM purchase_order_items WHERE product_id = ?
            ''', (self.selected_product['id'], self.selected_product['id']))
            
            has_transactions = any(row['count'] > 0 for row in cursor.fetchall())
            
            if has_transactions:
                # Soft delete (deactivate)
                cursor.execute('''
                    UPDATE products SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (self.selected_product['id'],))
                message = get_text('success.product_deactivated')
            else:
                # Hard delete
                cursor.execute('DELETE FROM products WHERE id = ?', (self.selected_product['id'],))
                message = get_text('success.product_deleted')
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="delete_product",
                table_name="products",
                record_id=self.selected_product['id']
            )
            
            show_message(get_text('common.success'), message, "success")
            self.clear_form()
            self.load_products()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error deleting product: {str(e)}", "error")
    
    def adjust_stock(self):
        """Open stock adjustment dialog"""
        if not self.selected_product:
            return
        
        # Check permissions
        if not auth_manager.has_permission('inventory', 'update'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        adjustment_window = StockAdjustmentWindow(self, self.selected_product)
        adjustment_window.grab_set()
    
    def clear_form(self):
        """Clear product form"""
        self.product_form.clear_form()
        self.selected_product = None
        
        # Disable update, delete, and stock adjustment buttons
        self.update_button.configure(state="disabled")
        self.delete_button.configure(state="disabled")
        self.adjust_stock_button.configure(state="disabled")
    
    def show_product_details(self, product_id):
        """Show detailed product information"""
        # Implement product details window as needed
        pass

class StockAdjustmentWindow(ctk.CTkToplevel):
    """Stock adjustment window"""
    
    def __init__(self, parent, product):
        super().__init__(parent)
        
        self.parent = parent
        self.product = product
        self.db = DatabaseManager()
        
        self.title(get_text('common.adjust_stock'))
        self.geometry("400x300")
        self.resizable(False, False)
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
    
    def setup_widgets(self):
        """Setup stock adjustment widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Product info
        info_frame = ctk.CTkFrame(main_frame)
        info_frame.pack(fill="x", pady=(0, 20))
        
        product_label = ctk.CTkLabel(
            info_frame,
            text=f"{get_text('common.product')}: {self.product['name']}",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        product_label.pack(pady=10)
        
        current_stock_label = ctk.CTkLabel(
            info_frame,
            text=f"{get_text('common.current_stock')}: {self.product['current_stock']}"
        )
        current_stock_label.pack()
        
        # Adjustment form
        form_frame = ctk.CTkFrame(main_frame)
        form_frame.pack(fill="x", pady=(0, 20))
        
        # Adjustment type
        type_label = ctk.CTkLabel(form_frame, text=get_text('common.adjustment_type'))
        type_label.grid(row=0, column=0, sticky="w", padx=10, pady=5)
        
        self.adjustment_type = ctk.CTkComboBox(
            form_frame,
            values=[get_text('common.increase'), get_text('common.decrease'), get_text('common.set_to')],
            width=200
        )
        self.adjustment_type.grid(row=0, column=1, padx=10, pady=5)
        self.adjustment_type.set(get_text('common.increase'))
        
        # Quantity
        qty_label = ctk.CTkLabel(form_frame, text=get_text('common.quantity'))
        qty_label.grid(row=1, column=0, sticky="w", padx=10, pady=5)
        
        self.quantity_var = tk.StringVar()
        self.quantity_entry = ctk.CTkEntry(form_frame, textvariable=self.quantity_var, width=200)
        self.quantity_entry.grid(row=1, column=1, padx=10, pady=5)
        
        # Reason
        reason_label = ctk.CTkLabel(form_frame, text=get_text('common.reason'))
        reason_label.grid(row=2, column=0, sticky="w", padx=10, pady=5)
        
        self.reason_var = tk.StringVar()
        self.reason_entry = ctk.CTkEntry(form_frame, textvariable=self.reason_var, width=200)
        self.reason_entry.grid(row=2, column=1, padx=10, pady=5)
        
        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.pack(fill="x")
        
        adjust_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.adjust'),
            command=self.adjust_stock
        )
        adjust_button.pack(side="left", padx=10, pady=10)
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.cancel'),
            command=self.destroy
        )
        cancel_button.pack(side="right", padx=10, pady=10)
    
    def adjust_stock(self):
        """Perform stock adjustment"""
        try:
            quantity = float(self.quantity_var.get() or 0)
            adjustment_type = self.adjustment_type.get()
            reason = self.reason_var.get()
            
            if quantity <= 0 and adjustment_type != get_text('common.set_to'):
                show_message(get_text('common.error'), get_text('error.invalid_quantity'), "error")
                return
            
            current_stock = float(self.product['current_stock'])
            
            # Calculate new stock
            if adjustment_type == get_text('common.increase'):
                new_stock = current_stock + quantity
                movement_type = 'in'
            elif adjustment_type == get_text('common.decrease'):
                new_stock = current_stock - quantity
                movement_type = 'out'
                if new_stock < 0:
                    show_message(get_text('common.error'), get_text('error.insufficient_stock'), "error")
                    return
            else:  # Set to
                new_stock = quantity
                movement_type = 'adjustment'
            
            # Update database
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Update product stock
            cursor.execute('''
                UPDATE products SET current_stock = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_stock, self.product['id']))
            
            # Record stock movement
            cursor.execute('''
                INSERT INTO stock_movements (
                    product_id, movement_type, quantity, reference_type,
                    notes, created_by, branch_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                self.product['id'], movement_type, 
                abs(new_stock - current_stock), 'adjustment',
                reason, auth_manager.get_user_id(),
                auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None
            ))
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="stock_adjustment",
                table_name="products",
                record_id=self.product['id'],
                new_values={
                    'old_stock': current_stock,
                    'new_stock': new_stock,
                    'adjustment_type': adjustment_type,
                    'quantity': quantity,
                    'reason': reason
                }
            )
            
            show_message(get_text('common.success'), get_text('success.stock_adjusted'), "success")
            self.parent.load_products()
            self.destroy()
            
        except ValueError:
            show_message(get_text('common.error'), get_text('error.invalid_number'), "error")
        except Exception as e:
            show_message(get_text('common.error'), f"Error adjusting stock: {str(e)}", "error")

if __name__ == "__main__":
    # Test inventory module
    app = ctk.CTk()
    inventory_window = InventoryWindow(app)
    app.mainloop()
