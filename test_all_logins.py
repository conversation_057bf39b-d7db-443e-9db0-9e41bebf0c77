#!/usr/bin/env python3
"""
Test All Login Windows
اختبار جميع نوافذ تسجيل الدخول
"""
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_login_windows():
    """Test different login window versions"""
    print("🔐 Testing All Login Windows...")
    print("=" * 50)
    
    # Setup
    try:
        import customtkinter as ctk
        from utils import setup_language
        from database import DatabaseManager
        
        # Setup database
        print("🗄️ Setting up database...")
        db = DatabaseManager()
        db.init_database()
        
        # Setup language
        setup_language()
        
        # Configure appearance
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        print("✅ Setup completed!")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        return
    
    # Test options
    options = [
        ("1", "Simple Login (مبسطة)", "simple_login.py", test_simple_login),
        ("2", "Enhanced Login (محسنة)", "gui.login_window", test_enhanced_login),
        ("3", "Standard Login (عادية)", "main.py", test_standard_login),
        ("4", "All Tests (جميع الاختبارات)", None, test_all)
    ]
    
    print("\n🎯 Choose login window to test:")
    print("-" * 40)
    
    for key, name, file, func in options:
        print(f"{key}. {name}")
    
    print("-" * 40)
    
    choice = input("Enter your choice (1-4): ").strip()
    
    for key, name, file, func in options:
        if choice == key:
            print(f"\n🚀 Testing {name}...")
            func()
            return
    
    print("❌ Invalid choice!")

def test_simple_login():
    """Test simple login window"""
    try:
        from simple_login import SimpleLoginWindow
        print("✅ Simple login window loaded")
        
        login = SimpleLoginWindow()
        login.mainloop()
        
    except Exception as e:
        print(f"❌ Simple login failed: {e}")
        import traceback
        traceback.print_exc()

def test_enhanced_login():
    """Test enhanced login window"""
    try:
        from gui.login_window import LoginWindow
        print("✅ Enhanced login window loaded")
        
        login = LoginWindow()
        login.mainloop()
        
    except Exception as e:
        print(f"❌ Enhanced login failed: {e}")
        import traceback
        traceback.print_exc()

def test_standard_login():
    """Test standard login by running main.py"""
    try:
        print("✅ Running standard application...")
        exec(open('main.py').read())
        
    except Exception as e:
        print(f"❌ Standard login failed: {e}")
        import traceback
        traceback.print_exc()

def test_all():
    """Test all login windows one by one"""
    print("🔄 Testing all login windows...")
    
    tests = [
        ("Simple Login", test_simple_login),
        ("Enhanced Login", test_enhanced_login),
        ("Standard Login", test_standard_login)
    ]
    
    for name, test_func in tests:
        print(f"\n🧪 Testing {name}...")
        try:
            test_func()
            print(f"✅ {name} test completed")
        except Exception as e:
            print(f"❌ {name} test failed: {e}")
        
        input("\nPress Enter to continue to next test...")

def main():
    """Main function"""
    try:
        print("🏢" + "=" * 48 + "🏢")
        print("🔐  Login Windows Test Suite - مجموعة اختبار نوافذ الدخول  🔐")
        print("🏢" + "=" * 48 + "🏢")
        print()
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8 or higher is required!")
            input("Press Enter to exit...")
            return
        
        print(f"✅ Python {sys.version.split()[0]} - OK")
        
        # Check required packages
        required = ['customtkinter', 'PIL']
        missing = []
        
        for package in required:
            try:
                __import__(package)
                print(f"✅ {package} - OK")
            except ImportError:
                print(f"❌ {package} - MISSING")
                missing.append(package)
        
        if missing:
            print(f"\n⚠️ Missing packages: {', '.join(missing)}")
            response = input("Install now? (y/n): ").lower().strip()
            
            if response in ['y', 'yes', 'نعم']:
                import subprocess
                for package in missing:
                    if package == 'PIL':
                        package = 'pillow'
                    print(f"Installing {package}...")
                    subprocess.run([sys.executable, "-m", "pip", "install", package])
                print("✅ Installation completed!")
            else:
                print("❌ Cannot continue without required packages")
                input("Press Enter to exit...")
                return
        
        print("\n🔐 Login Information for all tests:")
        print("   Username: admin")
        print("   Password: admin123")
        print()
        
        # Run tests
        test_login_windows()
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
