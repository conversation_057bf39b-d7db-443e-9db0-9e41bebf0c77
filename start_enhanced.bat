@echo off
chcp 65001 >nul
title Enhanced ERP System - نظام إدارة الأعمال المحسن

echo.
echo ================================================================
echo 🏢 Enhanced ERP System - نظام إدارة الأعمال المحسن
echo ================================================================
echo.

echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo 💡 Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
python --version

echo.
echo 📦 Checking required packages...

python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ CustomTkinter not found. Installing...
    pip install customtkinter
)

python -c "import PIL" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Pillow not found. Installing...
    pip install Pillow
)

python -c "import matplotlib" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Matplotlib not found. Installing...
    pip install matplotlib
)

python -c "import pandas" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Pandas not found. Installing...
    pip install pandas
)

python -c "import numpy" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Numpy not found. Installing...
    pip install numpy
)

echo ✅ All packages are ready

echo.
echo 🚀 Starting Enhanced ERP Application...
echo.

REM Try enhanced version first
if exist "run_enhanced_app.py" (
    echo 🌟 Starting Enhanced Version...
    python run_enhanced_app.py
) else if exist "main.py" (
    echo 🔄 Starting Standard Version...
    python main.py
) else if exist "run_app.py" (
    echo 🔄 Starting Alternative Version...
    python run_app.py
) else (
    echo ❌ No application file found!
    echo 💡 Please make sure you have the application files in this directory
    pause
    exit /b 1
)

echo.
echo 👋 Application closed
pause
