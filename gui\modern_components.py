"""
Modern UI Components for the ERP application
Professional and attractive design elements
"""
import customtkinter as ctk
import tkinter as tk
from PIL import Image, ImageTk
from utils import get_text, is_rtl, format_arabic_text

# Modern color palette
MODERN_COLORS = {
    # Primary colors
    'primary': '#1f538d',
    'primary_light': '#3d6fa7',
    'primary_dark': '#0f3a6b',
    
    # Secondary colors
    'secondary': '#14a085',
    'secondary_light': '#2eb89f',
    'secondary_dark': '#0a7a65',
    
    # Accent colors
    'accent': '#f39c12',
    'accent_light': '#f5b041',
    'accent_dark': '#d68910',
    
    # Status colors
    'success': '#27ae60',
    'success_light': '#52c882',
    'success_dark': '#1e8449',
    
    'warning': '#f39c12',
    'warning_light': '#f5b041',
    'warning_dark': '#d68910',
    
    'danger': '#e74c3c',
    'danger_light': '#ec7063',
    'danger_dark': '#c0392b',
    
    'info': '#3498db',
    'info_light': '#5dade2',
    'info_dark': '#2980b9',
    
    # Neutral colors
    'white': '#ffffff',
    'light': '#f8f9fa',
    'light_gray': '#e9ecef',
    'gray': '#6c757d',
    'dark_gray': '#495057',
    'dark': '#2c3e50',
    'black': '#000000',
    
    # Background colors
    'bg_primary': '#ffffff',
    'bg_secondary': '#f8f9fa',
    'bg_dark': '#2c3e50',
    
    # Text colors
    'text_primary': '#2c3e50',
    'text_secondary': '#6c757d',
    'text_light': '#adb5bd',
    'text_white': '#ffffff',
    
    # Border and shadow
    'border': '#dee2e6',
    'border_light': '#e9ecef',
    'shadow': '#00000015',
    'shadow_dark': '#00000030',
    
    # Gradient colors
    'gradient_blue_start': '#667eea',
    'gradient_blue_end': '#764ba2',
    'gradient_green_start': '#11998e',
    'gradient_green_end': '#38ef7d',
    'gradient_orange_start': '#fc4a1a',
    'gradient_orange_end': '#f7b733',
    'gradient_purple_start': '#667eea',
    'gradient_purple_end': '#764ba2'
}

# Modern fonts
MODERN_FONTS = {
    'display': ctk.CTkFont(size=36, weight="bold"),
    'title': ctk.CTkFont(size=28, weight="bold"),
    'heading': ctk.CTkFont(size=24, weight="bold"),
    'subheading': ctk.CTkFont(size=20, weight="bold"),
    'large': ctk.CTkFont(size=18),
    'body': ctk.CTkFont(size=16),
    'small': ctk.CTkFont(size=14),
    'caption': ctk.CTkFont(size=12),
    'tiny': ctk.CTkFont(size=10),
    
    # Special fonts
    'button': ctk.CTkFont(size=16, weight="bold"),
    'button_large': ctk.CTkFont(size=18, weight="bold"),
    'button_small': ctk.CTkFont(size=14, weight="bold"),
    
    'metric': ctk.CTkFont(size=32, weight="bold"),
    'metric_large': ctk.CTkFont(size=40, weight="bold"),
    'metric_small': ctk.CTkFont(size=24, weight="bold"),
    
    'label': ctk.CTkFont(size=14, weight="bold"),
    'label_small': ctk.CTkFont(size=12, weight="bold")
}

class ModernCard(ctk.CTkFrame):
    """Modern card component with shadow effect"""
    
    def __init__(self, parent, title=None, subtitle=None, icon=None, **kwargs):
        # Default styling
        default_kwargs = {
            'fg_color': MODERN_COLORS['white'],
            'corner_radius': 20,
            'border_width': 1,
            'border_color': MODERN_COLORS['border']
        }
        default_kwargs.update(kwargs)
        
        super().__init__(parent, **default_kwargs)
        
        if title or subtitle or icon:
            self.create_header(title, subtitle, icon)
    
    def create_header(self, title, subtitle, icon):
        """Create card header"""
        header_frame = ctk.CTkFrame(self, fg_color="transparent")
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        if icon:
            icon_label = ctk.CTkLabel(
                header_frame,
                text=icon,
                font=MODERN_FONTS['heading']
            )
            icon_label.pack(side="left", padx=(0, 15))
        
        if title or subtitle:
            text_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
            text_frame.pack(side="left", fill="x", expand=True)
            
            if title:
                title_label = ctk.CTkLabel(
                    text_frame,
                    text=title,
                    font=MODERN_FONTS['subheading'],
                    text_color=MODERN_COLORS['text_primary'],
                    anchor="w"
                )
                title_label.pack(anchor="w")
            
            if subtitle:
                subtitle_label = ctk.CTkLabel(
                    text_frame,
                    text=subtitle,
                    font=MODERN_FONTS['small'],
                    text_color=MODERN_COLORS['text_secondary'],
                    anchor="w"
                )
                subtitle_label.pack(anchor="w", pady=(2, 0))

class ModernButton(ctk.CTkButton):
    """Modern button with enhanced styling"""
    
    def __init__(self, parent, style="primary", size="medium", icon=None, **kwargs):
        # Style configurations
        styles = {
            'primary': {
                'fg_color': MODERN_COLORS['primary'],
                'hover_color': MODERN_COLORS['primary_light'],
                'text_color': MODERN_COLORS['text_white']
            },
            'secondary': {
                'fg_color': MODERN_COLORS['secondary'],
                'hover_color': MODERN_COLORS['secondary_light'],
                'text_color': MODERN_COLORS['text_white']
            },
            'success': {
                'fg_color': MODERN_COLORS['success'],
                'hover_color': MODERN_COLORS['success_light'],
                'text_color': MODERN_COLORS['text_white']
            },
            'warning': {
                'fg_color': MODERN_COLORS['warning'],
                'hover_color': MODERN_COLORS['warning_light'],
                'text_color': MODERN_COLORS['text_white']
            },
            'danger': {
                'fg_color': MODERN_COLORS['danger'],
                'hover_color': MODERN_COLORS['danger_light'],
                'text_color': MODERN_COLORS['text_white']
            },
            'outline': {
                'fg_color': 'transparent',
                'hover_color': MODERN_COLORS['light'],
                'text_color': MODERN_COLORS['primary'],
                'border_width': 2,
                'border_color': MODERN_COLORS['primary']
            },
            'ghost': {
                'fg_color': 'transparent',
                'hover_color': MODERN_COLORS['light'],
                'text_color': MODERN_COLORS['text_primary']
            }
        }
        
        # Size configurations
        sizes = {
            'small': {'height': 32, 'font': MODERN_FONTS['button_small']},
            'medium': {'height': 40, 'font': MODERN_FONTS['button']},
            'large': {'height': 50, 'font': MODERN_FONTS['button_large']}
        }
        
        # Apply style and size
        style_config = styles.get(style, styles['primary'])
        size_config = sizes.get(size, sizes['medium'])
        
        # Merge configurations
        default_kwargs = {
            'corner_radius': 12,
            **style_config,
            **size_config
        }
        default_kwargs.update(kwargs)
        
        # Add icon to text if provided
        if icon and 'text' in kwargs:
            default_kwargs['text'] = f"{icon} {kwargs['text']}"
        
        super().__init__(parent, **default_kwargs)

class ModernEntry(ctk.CTkEntry):
    """Modern entry field with enhanced styling"""
    
    def __init__(self, parent, label=None, icon=None, **kwargs):
        self.parent_container = parent
        
        # Create container if label or icon is provided
        if label or icon:
            self.container = ctk.CTkFrame(parent, fg_color="transparent")
            
            if label:
                label_frame = ctk.CTkFrame(self.container, fg_color="transparent")
                label_frame.pack(fill="x", pady=(0, 8))
                
                if icon:
                    icon_label = ctk.CTkLabel(
                        label_frame,
                        text=icon,
                        font=MODERN_FONTS['body']
                    )
                    icon_label.pack(side="left", padx=(0, 8))
                
                label_widget = ctk.CTkLabel(
                    label_frame,
                    text=label,
                    font=MODERN_FONTS['label'],
                    text_color=MODERN_COLORS['text_primary'],
                    anchor="w"
                )
                label_widget.pack(side="left")
            
            entry_parent = self.container
        else:
            entry_parent = parent
            self.container = None
        
        # Default styling
        default_kwargs = {
            'height': 45,
            'corner_radius': 12,
            'border_width': 2,
            'border_color': MODERN_COLORS['border'],
            'font': MODERN_FONTS['body']
        }
        default_kwargs.update(kwargs)
        
        super().__init__(entry_parent, **default_kwargs)
        
        # Focus effects
        self.bind("<FocusIn>", self._on_focus_in)
        self.bind("<FocusOut>", self._on_focus_out)
    
    def _on_focus_in(self, event):
        """Handle focus in event"""
        self.configure(border_color=MODERN_COLORS['primary'])
    
    def _on_focus_out(self, event):
        """Handle focus out event"""
        self.configure(border_color=MODERN_COLORS['border'])
    
    def pack(self, **kwargs):
        """Override pack to handle container"""
        if self.container:
            self.container.pack(**kwargs)
        else:
            super().pack(**kwargs)
    
    def grid(self, **kwargs):
        """Override grid to handle container"""
        if self.container:
            self.container.grid(**kwargs)
        else:
            super().grid(**kwargs)

class ModernMetricCard(ctk.CTkFrame):
    """Modern metric display card"""
    
    def __init__(self, parent, title, value, icon, color=None, trend=None, **kwargs):
        default_kwargs = {
            'fg_color': MODERN_COLORS['white'],
            'corner_radius': 20,
            'border_width': 2,
            'border_color': color or MODERN_COLORS['primary']
        }
        default_kwargs.update(kwargs)
        
        super().__init__(parent, **default_kwargs)
        
        # Header with colored background
        header_frame = ctk.CTkFrame(
            self,
            fg_color=color or MODERN_COLORS['primary'],
            corner_radius=15,
            height=60
        )
        header_frame.pack(fill="x", padx=15, pady=(15, 0))
        header_frame.pack_propagate(False)
        
        # Icon
        icon_label = ctk.CTkLabel(
            header_frame,
            text=icon,
            font=MODERN_FONTS['heading'],
            text_color=MODERN_COLORS['text_white']
        )
        icon_label.pack(side="left", padx=20, pady=15)
        
        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text=title,
            font=MODERN_FONTS['label'],
            text_color=MODERN_COLORS['text_white'],
            wraplength=150
        )
        title_label.pack(side="right", padx=20, pady=15)
        
        # Value section
        value_frame = ctk.CTkFrame(self, fg_color="transparent")
        value_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Main value
        self.value_label = ctk.CTkLabel(
            value_frame,
            text=str(value),
            font=MODERN_FONTS['metric'],
            text_color=color or MODERN_COLORS['primary']
        )
        self.value_label.pack(expand=True)
        
        # Trend indicator
        if trend:
            trend_frame = ctk.CTkFrame(value_frame, fg_color="transparent")
            trend_frame.pack(fill="x", pady=(10, 0))
            
            trend_color = MODERN_COLORS['success'] if trend.startswith('+') else MODERN_COLORS['danger']
            trend_icon = "📈" if trend.startswith('+') else "📉"
            
            trend_label = ctk.CTkLabel(
                trend_frame,
                text=f"{trend_icon} {trend}",
                font=MODERN_FONTS['caption'],
                text_color=trend_color
            )
            trend_label.pack()
    
    def update_value(self, new_value):
        """Update the metric value"""
        self.value_label.configure(text=str(new_value))

class ModernProgressBar(ctk.CTkFrame):
    """Modern progress bar with percentage"""
    
    def __init__(self, parent, value=0, max_value=100, color=None, show_percentage=True, **kwargs):
        default_kwargs = {
            'fg_color': MODERN_COLORS['light_gray'],
            'corner_radius': 10,
            'height': 20
        }
        default_kwargs.update(kwargs)
        
        super().__init__(parent, **default_kwargs)
        
        self.max_value = max_value
        self.current_value = value
        self.color = color or MODERN_COLORS['primary']
        self.show_percentage = show_percentage
        
        # Progress fill
        self.progress_fill = ctk.CTkFrame(
            self,
            fg_color=self.color,
            corner_radius=8,
            height=16
        )
        
        # Percentage label
        if show_percentage:
            self.percentage_label = ctk.CTkLabel(
                self,
                text=f"{self._calculate_percentage():.1f}%",
                font=MODERN_FONTS['caption'],
                text_color=MODERN_COLORS['text_white']
            )
        
        self.update_progress()
    
    def _calculate_percentage(self):
        """Calculate percentage"""
        return (self.current_value / self.max_value) * 100 if self.max_value > 0 else 0
    
    def update_progress(self):
        """Update progress display"""
        percentage = self._calculate_percentage()
        
        # Update fill width
        self.progress_fill.place(x=2, y=2, relwidth=percentage/100, height=16)
        
        # Update percentage text
        if self.show_percentage:
            self.percentage_label.place(relx=0.5, rely=0.5, anchor="center")
            self.percentage_label.configure(text=f"{percentage:.1f}%")
    
    def set_value(self, value):
        """Set new value"""
        self.current_value = max(0, min(value, self.max_value))
        self.update_progress()

class ModernNotification(ctk.CTkFrame):
    """Modern notification widget"""
    
    def __init__(self, parent, message, type="info", duration=5000, **kwargs):
        # Type configurations
        type_configs = {
            'info': {
                'color': MODERN_COLORS['info'],
                'icon': 'ℹ️'
            },
            'success': {
                'color': MODERN_COLORS['success'],
                'icon': '✅'
            },
            'warning': {
                'color': MODERN_COLORS['warning'],
                'icon': '⚠️'
            },
            'error': {
                'color': MODERN_COLORS['danger'],
                'icon': '❌'
            }
        }
        
        config = type_configs.get(type, type_configs['info'])
        
        default_kwargs = {
            'fg_color': MODERN_COLORS['white'],
            'corner_radius': 15,
            'border_width': 2,
            'border_color': config['color']
        }
        default_kwargs.update(kwargs)
        
        super().__init__(parent, **default_kwargs)
        
        # Content frame
        content_frame = ctk.CTkFrame(self, fg_color="transparent")
        content_frame.pack(fill="both", expand=True, padx=20, pady=15)
        
        # Icon
        icon_label = ctk.CTkLabel(
            content_frame,
            text=config['icon'],
            font=MODERN_FONTS['large']
        )
        icon_label.pack(side="left", padx=(0, 15))
        
        # Message
        message_label = ctk.CTkLabel(
            content_frame,
            text=message,
            font=MODERN_FONTS['body'],
            text_color=MODERN_COLORS['text_primary'],
            wraplength=300,
            anchor="w"
        )
        message_label.pack(side="left", fill="x", expand=True)
        
        # Close button
        close_button = ctk.CTkButton(
            content_frame,
            text="✕",
            width=30,
            height=30,
            font=MODERN_FONTS['small'],
            fg_color="transparent",
            text_color=MODERN_COLORS['text_secondary'],
            hover_color=MODERN_COLORS['light'],
            command=self.destroy
        )
        close_button.pack(side="right")
        
        # Auto-hide after duration
        if duration > 0:
            self.after(duration, self.destroy)

class ModernTabView(ctk.CTkTabview):
    """Modern tab view with enhanced styling"""
    
    def __init__(self, parent, **kwargs):
        default_kwargs = {
            'corner_radius': 15,
            'border_width': 1,
            'border_color': MODERN_COLORS['border'],
            'fg_color': MODERN_COLORS['white'],
            'segmented_button_fg_color': MODERN_COLORS['light'],
            'segmented_button_selected_color': MODERN_COLORS['primary'],
            'segmented_button_selected_hover_color': MODERN_COLORS['primary_light'],
            'text_color': MODERN_COLORS['text_primary'],
            'text_color_disabled': MODERN_COLORS['text_secondary']
        }
        default_kwargs.update(kwargs)
        
        super().__init__(parent, **default_kwargs)

# Utility functions for modern styling
def apply_modern_styling():
    """Apply modern styling to the application"""
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")

def create_gradient_frame(parent, start_color, end_color, **kwargs):
    """Create a frame with gradient-like effect (simulated)"""
    # Since CTk doesn't support real gradients, we simulate with layered frames
    container = ctk.CTkFrame(parent, fg_color="transparent")
    
    # Base frame
    base_frame = ctk.CTkFrame(
        container,
        fg_color=start_color,
        **kwargs
    )
    base_frame.pack(fill="both", expand=True)
    
    # Overlay frame for gradient effect
    overlay_frame = ctk.CTkFrame(
        base_frame,
        fg_color=end_color,
        **kwargs
    )
    overlay_frame.place(relx=0.5, rely=0, relwidth=0.5, relheight=1)
    
    return container, base_frame

def show_modern_message(parent, title, message, type="info"):
    """Show a modern message dialog"""
    dialog = ctk.CTkToplevel(parent)
    dialog.title(title)
    dialog.geometry("400x200")
    dialog.resizable(False, False)
    
    # Center on parent
    dialog.transient(parent)
    dialog.grab_set()
    
    # Main frame
    main_frame = ModernCard(dialog, title=title, icon="💬")
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # Message
    message_label = ctk.CTkLabel(
        main_frame,
        text=message,
        font=MODERN_FONTS['body'],
        text_color=MODERN_COLORS['text_primary'],
        wraplength=350
    )
    message_label.pack(padx=20, pady=20)
    
    # OK button
    ok_button = ModernButton(
        main_frame,
        text="موافق - OK",
        command=dialog.destroy,
        style="primary"
    )
    ok_button.pack(pady=(0, 20))
    
    return dialog
