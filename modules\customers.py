"""
Customer Management Module for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from gui.base_window import BaseWindow
from gui.components import FormFrame, DataTable, SearchFrame, show_message, show_confirmation
from utils import get_text, ValidationUtils, NumberUtils
from auth import auth_manager
from database import DatabaseManager

class CustomersWindow(ctk.CTkToplevel):
    """Customer management window"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title(get_text('modules.customers'))
        self.geometry("1200x800")
        
        self.db = DatabaseManager()
        self.selected_customer = None
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
        self.load_customers()
    
    def setup_widgets(self):
        """Setup customer management widgets"""
        # Main container
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=get_text('modules.customers'),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=20)
        
        # Left panel - Customer form
        self.create_customer_form(main_frame)
        
        # Right panel - Customer list
        self.create_customer_list(main_frame)
    
    def create_customer_form(self, parent):
        """Create customer form"""
        form_frame = ctk.CTkFrame(parent)
        form_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 5), pady=5)
        form_frame.grid_rowconfigure(1, weight=1)
        
        # Form title
        form_title = ctk.CTkLabel(
            form_frame,
            text=get_text('common.customer_details'),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        form_title.grid(row=0, column=0, pady=15)
        
        # Customer form
        self.customer_form = FormFrame(form_frame)
        self.customer_form.grid(row=1, column=0, sticky="nsew", padx=20, pady=10)
        
        # Add form fields
        self.customer_form.add_field(
            'name', get_text('common.name'), 
            required=True, width=250
        )
        
        self.customer_form.add_field(
            'contact_person', get_text('common.contact_person'), 
            width=250
        )
        
        self.customer_form.add_field(
            'email', get_text('common.email'), 
            validator=ValidationUtils.validate_email,
            width=250
        )
        
        self.customer_form.add_field(
            'phone', get_text('common.phone'), 
            validator=ValidationUtils.validate_phone,
            width=250
        )
        
        self.customer_form.add_field(
            'address', get_text('common.address'), 
            field_type="text", width=250
        )
        
        self.customer_form.add_field(
            'tax_number', get_text('common.tax_number'), 
            validator=ValidationUtils.validate_tax_number,
            width=250
        )
        
        self.customer_form.add_field(
            'payment_terms', get_text('common.payment_terms'), 
            field_type="number", width=250
        )
        
        self.customer_form.add_field(
            'credit_limit', get_text('common.credit_limit'), 
            field_type="number", width=250
        )
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(form_frame)
        buttons_frame.grid(row=2, column=0, sticky="ew", padx=20, pady=20)
        
        # Action buttons
        self.save_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.save'),
            command=self.save_customer,
            width=100
        )
        self.save_button.pack(side="left", padx=5)
        
        self.update_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.update'),
            command=self.update_customer,
            width=100,
            state="disabled"
        )
        self.update_button.pack(side="left", padx=5)
        
        self.delete_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.delete'),
            command=self.delete_customer,
            width=100,
            state="disabled",
            fg_color="red"
        )
        self.delete_button.pack(side="left", padx=5)
        
        self.clear_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.clear'),
            command=self.clear_form,
            width=100
        )
        self.clear_button.pack(side="left", padx=5)
    
    def create_customer_list(self, parent):
        """Create customer list"""
        list_frame = ctk.CTkFrame(parent)
        list_frame.grid(row=1, column=1, sticky="nsew", padx=(5, 0), pady=5)
        list_frame.grid_rowconfigure(2, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # List title
        list_title = ctk.CTkLabel(
            list_frame,
            text=get_text('common.customer_list'),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        list_title.grid(row=0, column=0, pady=15)
        
        # Search frame
        self.search_frame = SearchFrame(list_frame, on_search=self.search_customers)
        self.search_frame.grid(row=1, column=0, sticky="ew", padx=20, pady=10)
        
        # Customer table
        columns = {
            'id': {'text': 'ID', 'width': 50},
            'name': {'text': get_text('common.name'), 'width': 200},
            'contact_person': {'text': get_text('common.contact_person'), 'width': 150},
            'email': {'text': get_text('common.email'), 'width': 180},
            'phone': {'text': get_text('common.phone'), 'width': 120},
            'current_balance': {'text': get_text('common.balance'), 'width': 100, 'format': 'currency'},
            'is_active': {'text': get_text('common.status'), 'width': 80}
        }
        
        self.customer_table = DataTable(list_frame, columns)
        self.customer_table.grid(row=2, column=0, sticky="nsew", padx=20, pady=10)
        
        # Bind table events
        self.customer_table.tree.bind('<ButtonRelease-1>', self.on_customer_select)
        self.customer_table.tree.bind('<Double-1>', self.on_customer_double_click)
    
    def load_customers(self):
        """Load customers from database"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get branch filter
            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT id, name, contact_person, email, phone, 
                       current_balance, is_active, created_at
                FROM customers 
                {branch_filter}
                ORDER BY name
            ''', params)
            
            customers = []
            for row in cursor.fetchall():
                customer = dict(row)
                customer['status'] = get_text('common.active') if customer['is_active'] else get_text('common.inactive')
                customers.append(customer)
            
            self.customer_table.load_data(customers)
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading customers: {str(e)}", "error")
    
    def search_customers(self, search_term):
        """Search customers"""
        if not search_term:
            self.load_customers()
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get branch filter
            branch_filter = ""
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
            if auth_manager.get_user_branch():
                branch_filter = "AND branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT id, name, contact_person, email, phone, 
                       current_balance, is_active, created_at
                FROM customers 
                WHERE (name LIKE ? OR contact_person LIKE ? OR email LIKE ?)
                {branch_filter}
                ORDER BY name
            ''', params)
            
            customers = []
            for row in cursor.fetchall():
                customer = dict(row)
                customer['status'] = get_text('common.active') if customer['is_active'] else get_text('common.inactive')
                customers.append(customer)
            
            self.customer_table.load_data(customers)
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error searching customers: {str(e)}", "error")
    
    def on_customer_select(self, event):
        """Handle customer selection"""
        selected_customer = self.customer_table.get_selected_item()
        if selected_customer:
            self.selected_customer = selected_customer
            self.load_customer_details(selected_customer['id'])
            
            # Enable update and delete buttons
            self.update_button.configure(state="normal")
            self.delete_button.configure(state="normal")
    
    def on_customer_double_click(self, event):
        """Handle customer double click"""
        selected_customer = self.customer_table.get_selected_item()
        if selected_customer:
            self.show_customer_details(selected_customer['id'])
    
    def load_customer_details(self, customer_id):
        """Load customer details into form"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM customers WHERE id = ?
            ''', (customer_id,))
            
            customer = cursor.fetchone()
            conn.close()
            
            if customer:
                # Populate form
                self.customer_form.set_value('name', customer['name'])
                self.customer_form.set_value('contact_person', customer['contact_person'])
                self.customer_form.set_value('email', customer['email'])
                self.customer_form.set_value('phone', customer['phone'])
                self.customer_form.set_value('address', customer['address'])
                self.customer_form.set_value('tax_number', customer['tax_number'])
                self.customer_form.set_value('payment_terms', customer['payment_terms'])
                self.customer_form.set_value('credit_limit', customer['credit_limit'])
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading customer details: {str(e)}", "error")
    
    def save_customer(self):
        """Save new customer"""
        # Check permissions
        if not auth_manager.has_permission('customers', 'create'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Validate form
        errors = self.customer_form.validate_form()
        if errors:
            show_message(get_text('common.error'), '\n'.join(errors), "error")
            return
        
        try:
            # Get form data
            data = self.customer_form.get_form_data()
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if customer name already exists
            cursor.execute('''
                SELECT id FROM customers WHERE name = ? AND branch_id = ?
            ''', (data['name'], auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None))
            
            if cursor.fetchone():
                show_message(get_text('common.error'), get_text('error.customer_exists'), "error")
                conn.close()
                return
            
            # Insert customer
            cursor.execute('''
                INSERT INTO customers (
                    name, contact_person, email, phone, address, tax_number,
                    payment_terms, credit_limit, branch_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                data['name'], data['contact_person'], data['email'], data['phone'],
                data['address'], data['tax_number'], data['payment_terms'] or 30,
                data['credit_limit'] or 0,
                auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None
            ))
            
            customer_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="create_customer",
                table_name="customers",
                record_id=customer_id,
                new_values=data
            )
            
            show_message(get_text('common.success'), get_text('success.customer_saved'), "success")
            self.clear_form()
            self.load_customers()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error saving customer: {str(e)}", "error")
    
    def update_customer(self):
        """Update selected customer"""
        if not self.selected_customer:
            return
        
        # Check permissions
        if not auth_manager.has_permission('customers', 'update'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Validate form
        errors = self.customer_form.validate_form()
        if errors:
            show_message(get_text('common.error'), '\n'.join(errors), "error")
            return
        
        try:
            # Get form data
            data = self.customer_form.get_form_data()
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get old values for audit
            cursor.execute('SELECT * FROM customers WHERE id = ?', (self.selected_customer['id'],))
            old_values = dict(cursor.fetchone())
            
            # Update customer
            cursor.execute('''
                UPDATE customers SET
                    name = ?, contact_person = ?, email = ?, phone = ?,
                    address = ?, tax_number = ?, payment_terms = ?, credit_limit = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                data['name'], data['contact_person'], data['email'], data['phone'],
                data['address'], data['tax_number'], data['payment_terms'] or 30,
                data['credit_limit'] or 0, self.selected_customer['id']
            ))
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="update_customer",
                table_name="customers",
                record_id=self.selected_customer['id'],
                old_values=old_values,
                new_values=data
            )
            
            show_message(get_text('common.success'), get_text('success.customer_updated'), "success")
            self.load_customers()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error updating customer: {str(e)}", "error")
    
    def delete_customer(self):
        """Delete selected customer"""
        if not self.selected_customer:
            return
        
        # Check permissions
        if not auth_manager.has_permission('customers', 'delete'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Confirm deletion
        if not show_confirmation(
            get_text('common.confirm'),
            f"{get_text('common.confirm_delete')} {self.selected_customer['name']}?"
        ):
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if customer has transactions
            cursor.execute('''
                SELECT COUNT(*) as count FROM sales_orders WHERE customer_id = ?
            ''', (self.selected_customer['id'],))
            
            if cursor.fetchone()['count'] > 0:
                # Soft delete (deactivate)
                cursor.execute('''
                    UPDATE customers SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (self.selected_customer['id'],))
                message = get_text('success.customer_deactivated')
            else:
                # Hard delete
                cursor.execute('DELETE FROM customers WHERE id = ?', (self.selected_customer['id'],))
                message = get_text('success.customer_deleted')
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="delete_customer",
                table_name="customers",
                record_id=self.selected_customer['id']
            )
            
            show_message(get_text('common.success'), message, "success")
            self.clear_form()
            self.load_customers()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error deleting customer: {str(e)}", "error")
    
    def clear_form(self):
        """Clear customer form"""
        self.customer_form.clear_form()
        self.selected_customer = None
        
        # Disable update and delete buttons
        self.update_button.configure(state="disabled")
        self.delete_button.configure(state="disabled")
    
    def show_customer_details(self, customer_id):
        """Show detailed customer information"""
        details_window = CustomerDetailsWindow(self, customer_id)
        details_window.grab_set()

class CustomerDetailsWindow(ctk.CTkToplevel):
    """Customer details window"""
    
    def __init__(self, parent, customer_id):
        super().__init__(parent)
        
        self.customer_id = customer_id
        self.db = DatabaseManager()
        
        self.title(get_text('common.customer_details'))
        self.geometry("800x600")
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
        self.load_customer_data()
    
    def setup_widgets(self):
        """Setup customer details widgets"""
        # Main notebook
        self.notebook = ctk.CTkTabview(self)
        self.notebook.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Customer info tab
        self.notebook.add(get_text('common.customer_info'))
        self.create_info_tab()
        
        # Transactions tab
        self.notebook.add(get_text('common.transactions'))
        self.create_transactions_tab()
        
        # Invoices tab
        self.notebook.add(get_text('common.invoices'))
        self.create_invoices_tab()
    
    def create_info_tab(self):
        """Create customer info tab"""
        info_frame = self.notebook.tab(get_text('common.customer_info'))
        
        # Customer info will be populated in load_customer_data
        self.info_labels = {}
    
    def create_transactions_tab(self):
        """Create transactions tab"""
        trans_frame = self.notebook.tab(get_text('common.transactions'))
        
        # Transactions table
        columns = {
            'date': {'text': get_text('common.date'), 'width': 100},
            'type': {'text': get_text('common.type'), 'width': 100},
            'reference': {'text': get_text('common.reference'), 'width': 120},
            'amount': {'text': get_text('common.amount'), 'width': 100, 'format': 'currency'},
            'status': {'text': get_text('common.status'), 'width': 100}
        }
        
        self.transactions_table = DataTable(trans_frame, columns)
        self.transactions_table.pack(fill="both", expand=True, padx=10, pady=10)
    
    def create_invoices_tab(self):
        """Create invoices tab"""
        invoices_frame = self.notebook.tab(get_text('common.invoices'))
        
        # Invoices table
        columns = {
            'invoice_number': {'text': get_text('common.invoice_number'), 'width': 120},
            'date': {'text': get_text('common.date'), 'width': 100},
            'due_date': {'text': get_text('common.due_date'), 'width': 100},
            'total_amount': {'text': get_text('common.total'), 'width': 100, 'format': 'currency'},
            'paid_amount': {'text': get_text('common.paid'), 'width': 100, 'format': 'currency'},
            'balance_due': {'text': get_text('common.balance'), 'width': 100, 'format': 'currency'},
            'status': {'text': get_text('common.status'), 'width': 100}
        }
        
        self.invoices_table = DataTable(invoices_frame, columns)
        self.invoices_table.pack(fill="both", expand=True, padx=10, pady=10)
    
    def load_customer_data(self):
        """Load customer data"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get customer info
            cursor.execute('SELECT * FROM customers WHERE id = ?', (self.customer_id,))
            customer = cursor.fetchone()
            
            if customer:
                self.display_customer_info(dict(customer))
                self.load_transactions()
                self.load_invoices()
            
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading customer data: {str(e)}", "error")
    
    def display_customer_info(self, customer):
        """Display customer information"""
        info_frame = self.notebook.tab(get_text('common.customer_info'))
        
        # Clear existing widgets
        for widget in info_frame.winfo_children():
            widget.destroy()
        
        # Customer info
        info_data = [
            (get_text('common.name'), customer['name']),
            (get_text('common.contact_person'), customer['contact_person']),
            (get_text('common.email'), customer['email']),
            (get_text('common.phone'), customer['phone']),
            (get_text('common.address'), customer['address']),
            (get_text('common.tax_number'), customer['tax_number']),
            (get_text('common.payment_terms'), f"{customer['payment_terms']} {get_text('common.days')}"),
            (get_text('common.credit_limit'), NumberUtils.format_currency(customer['credit_limit'])),
            (get_text('common.current_balance'), NumberUtils.format_currency(customer['current_balance'])),
            (get_text('common.status'), get_text('common.active') if customer['is_active'] else get_text('common.inactive'))
        ]
        
        for i, (label, value) in enumerate(info_data):
            label_widget = ctk.CTkLabel(info_frame, text=f"{label}:", anchor="w")
            label_widget.grid(row=i, column=0, sticky="w", padx=20, pady=5)
            
            value_widget = ctk.CTkLabel(info_frame, text=str(value or ''), anchor="w")
            value_widget.grid(row=i, column=1, sticky="w", padx=20, pady=5)
    
    def load_transactions(self):
        """Load customer transactions"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT order_date as date, 'Sales Order' as type, 
                       so_number as reference, total_amount as amount, status
                FROM sales_orders 
                WHERE customer_id = ?
                ORDER BY order_date DESC
            ''', (self.customer_id,))
            
            transactions = [dict(row) for row in cursor.fetchall()]
            self.transactions_table.load_data(transactions)
            
            conn.close()
            
        except Exception as e:
            print(f"Error loading transactions: {e}")
    
    def load_invoices(self):
        """Load customer invoices"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT invoice_number, invoice_date as date, due_date,
                       total_amount, paid_amount, balance_due, status
                FROM invoices 
                WHERE customer_id = ?
                ORDER BY invoice_date DESC
            ''', (self.customer_id,))
            
            invoices = [dict(row) for row in cursor.fetchall()]
            self.invoices_table.load_data(invoices)
            
            conn.close()
            
        except Exception as e:
            print(f"Error loading invoices: {e}")

if __name__ == "__main__":
    # Test customers module
    app = ctk.CTk()
    customers_window = CustomersWindow(app)
    app.mainloop()
