#!/usr/bin/env python3
"""
Fixed ERP Application with Enhanced Login
تطبيق إدارة الأعمال مع نافذة دخول محسنة
"""
import sys
import os
import traceback
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def print_banner():
    """Print application banner"""
    print("🏢" + "=" * 68 + "🏢")
    print("🌟  Enhanced ERP System - نظام إدارة الأعمال المحسن  🌟")
    print("🏢" + "=" * 68 + "🏢")
    print()

def check_requirements():
    """Check if all requirements are installed"""
    required_modules = [
        'customtkinter',
        'PIL',
        'matplotlib',
        'pandas',
        'numpy'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - OK")
        except ImportError:
            print(f"❌ {module} - MISSING")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️ Missing modules: {', '.join(missing)}")
        print("💡 Install with: pip install customtkinter pillow matplotlib pandas numpy")
        return False
    
    return True

def setup_database():
    """Setup database if needed"""
    try:
        from database import DatabaseManager
        db = DatabaseManager()
        
        # Check if database exists and is properly initialized
        if not Path("erp_database.db").exists():
            print("🗄️ Setting up database...")
            db.init_database()
            print("✅ Database setup completed")
        else:
            print("✅ Database found")
        
        return True
        
    except Exception as e:
        print(f"❌ Database setup error: {e}")
        return False

def run_application():
    """Run the main application"""
    try:
        print("🚀 Starting Enhanced ERP Application...")
        
        # Import required modules
        import customtkinter as ctk
        from gui.login_window import LoginWindow
        from auth import auth_manager
        from utils import setup_language
        
        # Configure appearance
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        # Setup language
        setup_language()
        
        print("🔐 Opening login window...")
        
        # Create and show login window
        login_window = LoginWindow()
        
        # Override the open_main_application method to use enhanced dashboard
        original_open_main = login_window.open_main_application
        
        def open_enhanced_main():
            """Open enhanced main application"""
            try:
                # Try enhanced dashboard first
                from gui.enhanced_dashboard import EnhancedDashboard
                print("🌟 Loading Enhanced Dashboard...")
                dashboard = EnhancedDashboard()
                dashboard.mainloop()
            except Exception as e:
                print(f"⚠️ Enhanced dashboard failed: {e}")
                print("🔄 Falling back to standard dashboard...")
                # Fallback to standard dashboard
                original_open_main()
        
        login_window.open_main_application = open_enhanced_main
        
        # Start the application
        login_window.mainloop()
        
        print("👋 Application closed successfully!")
        
    except Exception as e:
        print(f"❌ Error running application: {e}")
        traceback.print_exc()
        return False
    
    return True

def main():
    """Main function"""
    try:
        print_banner()
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8 or higher is required!")
            print(f"   Current version: {sys.version}")
            input("\nPress Enter to exit...")
            return
        
        print(f"✅ Python {sys.version.split()[0]} - OK")
        
        # Check requirements
        print("\n📦 Checking requirements...")
        if not check_requirements():
            response = input("\n🤔 Install missing packages now? (y/n): ").lower().strip()
            if response in ['y', 'yes', 'نعم']:
                try:
                    import subprocess
                    packages = ['customtkinter', 'pillow', 'matplotlib', 'pandas', 'numpy']
                    for package in packages:
                        print(f"Installing {package}...")
                        subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                     capture_output=True, check=True)
                    print("✅ All packages installed!")
                except Exception as e:
                    print(f"❌ Installation failed: {e}")
                    input("\nPress Enter to exit...")
                    return
            else:
                print("❌ Cannot continue without required packages")
                input("\nPress Enter to exit...")
                return
        
        # Setup database
        print("\n🗄️ Setting up database...")
        if not setup_database():
            print("⚠️ Database setup failed, but continuing...")
        
        # Show login info
        print("\n🔐 Login Information:")
        print("   Username: admin")
        print("   Password: admin123")
        
        print("\n🎯 Ready to start!")
        input("Press Enter to continue...")
        
        # Run application
        if not run_application():
            print("❌ Application failed to start")
            input("\nPress Enter to exit...")
            return
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Application interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        traceback.print_exc()
    finally:
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
