"""
Theme Manager for the ERP application
Manages modern themes, colors, and styling
"""
import customtkinter as ctk
import json
import os
from pathlib import Path

class ThemeManager:
    """Manages application themes and styling"""
    
    def __init__(self):
        self.current_theme = "modern_light"
        self.themes = self.load_themes()
        self.apply_theme(self.current_theme)
    
    def load_themes(self):
        """Load available themes"""
        return {
            "modern_light": {
                "name": "Modern Light",
                "name_ar": "عصري فاتح",
                "appearance_mode": "light",
                "colors": {
                    # Primary palette
                    "primary": "#1f538d",
                    "primary_light": "#3d6fa7", 
                    "primary_dark": "#0f3a6b",
                    "primary_hover": "#2a5f98",
                    
                    # Secondary palette
                    "secondary": "#14a085",
                    "secondary_light": "#2eb89f",
                    "secondary_dark": "#0a7a65",
                    "secondary_hover": "#1fb396",
                    
                    # Accent colors
                    "accent": "#f39c12",
                    "accent_light": "#f5b041",
                    "accent_dark": "#d68910",
                    
                    # Status colors
                    "success": "#27ae60",
                    "success_light": "#52c882",
                    "success_dark": "#1e8449",
                    
                    "warning": "#f39c12", 
                    "warning_light": "#f5b041",
                    "warning_dark": "#d68910",
                    
                    "danger": "#e74c3c",
                    "danger_light": "#ec7063",
                    "danger_dark": "#c0392b",
                    
                    "info": "#3498db",
                    "info_light": "#5dade2",
                    "info_dark": "#2980b9",
                    
                    # Neutral colors
                    "white": "#ffffff",
                    "light": "#f8f9fa",
                    "light_gray": "#e9ecef",
                    "gray": "#6c757d",
                    "dark_gray": "#495057",
                    "dark": "#2c3e50",
                    "black": "#000000",
                    
                    # Background colors
                    "bg_primary": "#ffffff",
                    "bg_secondary": "#f8f9fa",
                    "bg_tertiary": "#e9ecef",
                    "bg_card": "#ffffff",
                    "bg_sidebar": "#f8f9fa",
                    "bg_header": "#1f538d",
                    
                    # Text colors
                    "text_primary": "#2c3e50",
                    "text_secondary": "#6c757d",
                    "text_light": "#adb5bd",
                    "text_white": "#ffffff",
                    "text_muted": "#868e96",
                    
                    # Border and effects
                    "border": "#dee2e6",
                    "border_light": "#e9ecef",
                    "border_dark": "#adb5bd",
                    "shadow": "#00000015",
                    "shadow_dark": "#00000030",
                    "focus": "#80bdff",
                    
                    # Gradients
                    "gradient_primary_start": "#667eea",
                    "gradient_primary_end": "#764ba2",
                    "gradient_success_start": "#11998e", 
                    "gradient_success_end": "#38ef7d",
                    "gradient_warning_start": "#fc4a1a",
                    "gradient_warning_end": "#f7b733"
                },
                "fonts": {
                    "display": {"size": 36, "weight": "bold"},
                    "title": {"size": 28, "weight": "bold"},
                    "heading": {"size": 24, "weight": "bold"},
                    "subheading": {"size": 20, "weight": "bold"},
                    "large": {"size": 18, "weight": "normal"},
                    "body": {"size": 16, "weight": "normal"},
                    "small": {"size": 14, "weight": "normal"},
                    "caption": {"size": 12, "weight": "normal"},
                    "tiny": {"size": 10, "weight": "normal"},
                    "button": {"size": 16, "weight": "bold"},
                    "button_large": {"size": 18, "weight": "bold"},
                    "button_small": {"size": 14, "weight": "bold"},
                    "metric": {"size": 32, "weight": "bold"},
                    "metric_large": {"size": 40, "weight": "bold"},
                    "label": {"size": 14, "weight": "bold"}
                },
                "spacing": {
                    "xs": 4,
                    "sm": 8,
                    "md": 16,
                    "lg": 24,
                    "xl": 32,
                    "xxl": 48
                },
                "border_radius": {
                    "small": 8,
                    "medium": 12,
                    "large": 16,
                    "xl": 20,
                    "round": 50
                }
            },
            
            "modern_dark": {
                "name": "Modern Dark",
                "name_ar": "عصري داكن",
                "appearance_mode": "dark",
                "colors": {
                    # Primary palette (adjusted for dark theme)
                    "primary": "#4a90e2",
                    "primary_light": "#6ba3e8",
                    "primary_dark": "#357abd",
                    "primary_hover": "#5a9ae5",
                    
                    # Secondary palette
                    "secondary": "#2eb89f",
                    "secondary_light": "#4cc9b0",
                    "secondary_dark": "#1e8a7a",
                    
                    # Background colors (dark theme)
                    "bg_primary": "#1a1a1a",
                    "bg_secondary": "#2d2d2d",
                    "bg_tertiary": "#3d3d3d",
                    "bg_card": "#2d2d2d",
                    "bg_sidebar": "#1a1a1a",
                    "bg_header": "#1a1a1a",
                    
                    # Text colors (dark theme)
                    "text_primary": "#ffffff",
                    "text_secondary": "#b0b0b0",
                    "text_light": "#808080",
                    "text_white": "#ffffff",
                    "text_muted": "#666666",
                    
                    # Other colors remain similar but adjusted
                    "white": "#ffffff",
                    "light": "#3d3d3d",
                    "border": "#404040",
                    "shadow": "#00000030"
                }
            },
            
            "professional_blue": {
                "name": "Professional Blue",
                "name_ar": "أزرق مهني",
                "appearance_mode": "light",
                "colors": {
                    "primary": "#0066cc",
                    "secondary": "#00a86b",
                    "accent": "#ff6b35",
                    "bg_primary": "#f5f7fa",
                    "bg_header": "#0066cc",
                    "text_primary": "#1a1a1a"
                }
            },
            
            "elegant_green": {
                "name": "Elegant Green", 
                "name_ar": "أخضر أنيق",
                "appearance_mode": "light",
                "colors": {
                    "primary": "#2d7d32",
                    "secondary": "#1976d2",
                    "accent": "#ff9800",
                    "bg_primary": "#f1f8e9",
                    "bg_header": "#2d7d32",
                    "text_primary": "#1b5e20"
                }
            }
        }
    
    def apply_theme(self, theme_name):
        """Apply a theme to the application"""
        if theme_name not in self.themes:
            theme_name = "modern_light"
        
        theme = self.themes[theme_name]
        self.current_theme = theme_name
        
        # Set appearance mode
        ctk.set_appearance_mode(theme.get("appearance_mode", "light"))
        
        # Apply color theme
        self.apply_color_theme(theme["colors"])
        
        # Store current theme
        self.save_current_theme()
    
    def apply_color_theme(self, colors):
        """Apply color theme to CustomTkinter"""
        # Create a custom color theme
        custom_theme = {
            "CTk": {
                "fg_color": [colors["bg_primary"], colors.get("bg_primary", "#1a1a1a")]
            },
            "CTkToplevel": {
                "fg_color": [colors["bg_primary"], colors.get("bg_primary", "#1a1a1a")]
            },
            "CTkFrame": {
                "corner_radius": 12,
                "border_width": 1,
                "fg_color": [colors["bg_card"], colors.get("bg_card", "#2d2d2d")],
                "border_color": [colors["border"], colors.get("border", "#404040")]
            },
            "CTkButton": {
                "corner_radius": 12,
                "border_width": 0,
                "fg_color": [colors["primary"], colors["primary"]],
                "hover_color": [colors.get("primary_hover", colors["primary"]), colors.get("primary_hover", colors["primary"])],
                "border_color": [colors["primary"], colors["primary"]],
                "text_color": [colors["text_white"], colors["text_white"]],
                "text_color_disabled": [colors["text_light"], colors["text_light"]]
            },
            "CTkLabel": {
                "corner_radius": 0,
                "fg_color": "transparent",
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")]
            },
            "CTkEntry": {
                "corner_radius": 12,
                "border_width": 2,
                "fg_color": [colors["white"], colors.get("bg_secondary", "#2d2d2d")],
                "border_color": [colors["border"], colors.get("border", "#404040")],
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")],
                "placeholder_text_color": [colors["text_secondary"], colors.get("text_secondary", "#b0b0b0")]
            },
            "CTkTextbox": {
                "corner_radius": 12,
                "border_width": 1,
                "fg_color": [colors["white"], colors.get("bg_secondary", "#2d2d2d")],
                "border_color": [colors["border"], colors.get("border", "#404040")],
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")]
            },
            "CTkScrollbar": {
                "corner_radius": 6,
                "border_spacing": 4,
                "fg_color": [colors["light"], colors.get("bg_tertiary", "#3d3d3d")],
                "button_color": [colors["gray"], colors.get("text_secondary", "#b0b0b0")],
                "button_hover_color": [colors["dark_gray"], colors.get("text_primary", "#ffffff")]
            },
            "CTkCheckBox": {
                "corner_radius": 6,
                "border_width": 2,
                "fg_color": [colors["primary"], colors["primary"]],
                "border_color": [colors["border"], colors.get("border", "#404040")],
                "hover_color": [colors.get("primary_hover", colors["primary"]), colors.get("primary_hover", colors["primary"])],
                "checkmark_color": [colors["text_white"], colors["text_white"]],
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")]
            },
            "CTkSwitch": {
                "corner_radius": 20,
                "border_width": 3,
                "button_length": 0,
                "fg_color": [colors["border"], colors.get("border", "#404040")],
                "progress_color": [colors["primary"], colors["primary"]],
                "button_color": [colors["white"], colors.get("text_white", "#ffffff")],
                "button_hover_color": [colors["light_gray"], colors.get("text_light", "#808080")],
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")]
            },
            "CTkRadioButton": {
                "corner_radius": 10,
                "border_width_checked": 5,
                "border_width_unchecked": 3,
                "fg_color": [colors["primary"], colors["primary"]],
                "border_color": [colors["border"], colors.get("border", "#404040")],
                "hover_color": [colors.get("primary_hover", colors["primary"]), colors.get("primary_hover", colors["primary"])],
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")]
            },
            "CTkProgressBar": {
                "corner_radius": 10,
                "border_width": 0,
                "fg_color": [colors["light_gray"], colors.get("bg_tertiary", "#3d3d3d")],
                "progress_color": [colors["primary"], colors["primary"]]
            },
            "CTkSlider": {
                "corner_radius": 10,
                "button_corner_radius": 10,
                "border_width": 5,
                "button_length": 0,
                "fg_color": [colors["light_gray"], colors.get("bg_tertiary", "#3d3d3d")],
                "progress_color": [colors["primary"], colors["primary"]],
                "button_color": [colors["primary"], colors["primary"]],
                "button_hover_color": [colors.get("primary_hover", colors["primary"]), colors.get("primary_hover", colors["primary"])]
            },
            "CTkOptionMenu": {
                "corner_radius": 12,
                "fg_color": [colors["primary"], colors["primary"]],
                "button_color": [colors.get("primary_hover", colors["primary"]), colors.get("primary_hover", colors["primary"])],
                "button_hover_color": [colors.get("primary_dark", colors["primary"]), colors.get("primary_dark", colors["primary"])],
                "text_color": [colors["text_white"], colors["text_white"]],
                "text_color_disabled": [colors["text_light"], colors["text_light"]]
            },
            "CTkComboBox": {
                "corner_radius": 12,
                "border_width": 2,
                "fg_color": [colors["white"], colors.get("bg_secondary", "#2d2d2d")],
                "border_color": [colors["border"], colors.get("border", "#404040")],
                "button_color": [colors["primary"], colors["primary"]],
                "button_hover_color": [colors.get("primary_hover", colors["primary"]), colors.get("primary_hover", colors["primary"])],
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")],
                "text_color_disabled": [colors["text_light"], colors["text_light"]]
            },
            "CTkScrollableFrame": {
                "corner_radius": 12,
                "border_width": 1,
                "fg_color": [colors["bg_card"], colors.get("bg_card", "#2d2d2d")],
                "border_color": [colors["border"], colors.get("border", "#404040")]
            },
            "CTkSegmentedButton": {
                "corner_radius": 12,
                "border_width": 2,
                "fg_color": [colors["light"], colors.get("bg_tertiary", "#3d3d3d")],
                "selected_color": [colors["primary"], colors["primary"]],
                "selected_hover_color": [colors.get("primary_hover", colors["primary"]), colors.get("primary_hover", colors["primary"])],
                "unselected_color": [colors["light"], colors.get("bg_tertiary", "#3d3d3d")],
                "unselected_hover_color": [colors["light_gray"], colors.get("bg_secondary", "#2d2d2d")],
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")],
                "text_color_disabled": [colors["text_light"], colors["text_light"]]
            },
            "CTkTabview": {
                "corner_radius": 12,
                "border_width": 1,
                "fg_color": [colors["bg_card"], colors.get("bg_card", "#2d2d2d")],
                "border_color": [colors["border"], colors.get("border", "#404040")],
                "segmented_button_fg_color": [colors["light"], colors.get("bg_tertiary", "#3d3d3d")],
                "segmented_button_selected_color": [colors["primary"], colors["primary"]],
                "segmented_button_selected_hover_color": [colors.get("primary_hover", colors["primary"]), colors.get("primary_hover", colors["primary"])],
                "segmented_button_unselected_color": [colors["light"], colors.get("bg_tertiary", "#3d3d3d")],
                "segmented_button_unselected_hover_color": [colors["light_gray"], colors.get("bg_secondary", "#2d2d2d")],
                "text_color": [colors["text_primary"], colors.get("text_primary", "#ffffff")],
                "text_color_disabled": [colors["text_light"], colors["text_light"]]
            }
        }
        
        # Apply the custom theme
        try:
            # Save theme to temporary file
            theme_path = Path("temp_theme.json")
            with open(theme_path, 'w', encoding='utf-8') as f:
                json.dump(custom_theme, f, indent=2)
            
            # Load the custom theme
            ctk.set_default_color_theme(str(theme_path))
            
            # Clean up
            if theme_path.exists():
                theme_path.unlink()
                
        except Exception as e:
            print(f"Error applying theme: {e}")
            # Fallback to default theme
            ctk.set_default_color_theme("blue")
    
    def get_current_colors(self):
        """Get current theme colors"""
        return self.themes[self.current_theme]["colors"]
    
    def get_current_fonts(self):
        """Get current theme fonts"""
        fonts = self.themes[self.current_theme].get("fonts", {})
        ctk_fonts = {}
        
        for name, config in fonts.items():
            ctk_fonts[name] = ctk.CTkFont(
                size=config["size"],
                weight=config["weight"]
            )
        
        return ctk_fonts
    
    def get_available_themes(self):
        """Get list of available themes"""
        return [(name, theme["name"], theme.get("name_ar", theme["name"])) 
                for name, theme in self.themes.items()]
    
    def save_current_theme(self):
        """Save current theme to settings"""
        try:
            settings_dir = Path("settings")
            settings_dir.mkdir(exist_ok=True)
            
            settings_file = settings_dir / "theme.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump({"current_theme": self.current_theme}, f)
        except Exception as e:
            print(f"Error saving theme settings: {e}")
    
    def load_saved_theme(self):
        """Load saved theme from settings"""
        try:
            settings_file = Path("settings") / "theme.json"
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    saved_theme = settings.get("current_theme", "modern_light")
                    if saved_theme in self.themes:
                        self.apply_theme(saved_theme)
        except Exception as e:
            print(f"Error loading theme settings: {e}")
    
    def create_styled_widget(self, widget_class, parent, style_overrides=None, **kwargs):
        """Create a widget with current theme styling"""
        colors = self.get_current_colors()
        
        # Default styling based on widget type
        if widget_class == ctk.CTkButton:
            default_style = {
                'fg_color': colors['primary'],
                'hover_color': colors.get('primary_hover', colors['primary']),
                'corner_radius': 12,
                'font': ctk.CTkFont(size=16, weight="bold")
            }
        elif widget_class == ctk.CTkFrame:
            default_style = {
                'fg_color': colors['bg_card'],
                'corner_radius': 12,
                'border_width': 1,
                'border_color': colors['border']
            }
        elif widget_class == ctk.CTkEntry:
            default_style = {
                'fg_color': colors['white'],
                'border_color': colors['border'],
                'corner_radius': 12,
                'border_width': 2,
                'font': ctk.CTkFont(size=14)
            }
        else:
            default_style = {}
        
        # Apply style overrides
        if style_overrides:
            default_style.update(style_overrides)
        
        # Merge with kwargs
        final_kwargs = {**default_style, **kwargs}
        
        return widget_class(parent, **final_kwargs)

# Global theme manager instance
theme_manager = ThemeManager()

# Convenience functions
def get_colors():
    """Get current theme colors"""
    return theme_manager.get_current_colors()

def get_fonts():
    """Get current theme fonts"""
    return theme_manager.get_current_fonts()

def apply_theme(theme_name):
    """Apply a theme"""
    theme_manager.apply_theme(theme_name)

def get_available_themes():
    """Get available themes"""
    return theme_manager.get_available_themes()

def create_styled_button(parent, text, style="primary", **kwargs):
    """Create a styled button"""
    colors = get_colors()
    
    style_configs = {
        'primary': {
            'fg_color': colors['primary'],
            'hover_color': colors.get('primary_hover', colors['primary'])
        },
        'secondary': {
            'fg_color': colors['secondary'],
            'hover_color': colors.get('secondary_hover', colors['secondary'])
        },
        'success': {
            'fg_color': colors['success'],
            'hover_color': colors.get('success_light', colors['success'])
        },
        'warning': {
            'fg_color': colors['warning'],
            'hover_color': colors.get('warning_light', colors['warning'])
        },
        'danger': {
            'fg_color': colors['danger'],
            'hover_color': colors.get('danger_light', colors['danger'])
        }
    }
    
    style_config = style_configs.get(style, style_configs['primary'])
    
    return ctk.CTkButton(
        parent,
        text=text,
        corner_radius=12,
        font=ctk.CTkFont(size=16, weight="bold"),
        **style_config,
        **kwargs
    )

def create_styled_frame(parent, **kwargs):
    """Create a styled frame"""
    colors = get_colors()
    
    return ctk.CTkFrame(
        parent,
        fg_color=colors['bg_card'],
        corner_radius=12,
        border_width=1,
        border_color=colors['border'],
        **kwargs
    )
