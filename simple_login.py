#!/usr/bin/env python3
"""
Simple Login Window for Testing
نافذة دخول مبسطة للاختبار
"""
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from utils import get_text, is_rtl, format_arabic_text, setup_language
from auth import auth_manager
from database import DatabaseManager

# Configure appearance
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class SimpleLoginWindow(ctk.CTk):
    """Simple login window for testing"""
    
    def __init__(self):
        super().__init__()
        
        # Window configuration
        self.title("تسجيل الدخول - Login")
        self.geometry("500x600")
        self.resizable(False, False)
        
        # Center window
        self.center_window()
        
        # Variables
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.remember_var = tk.BooleanVar()
        
        # Setup widgets
        self.setup_widgets()
        
        # Bind events
        self.bind('<Return>', lambda e: self.login())
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Focus on username entry
        self.username_entry.focus()
    
    def center_window(self):
        """Center window on screen"""
        self.update_idletasks()
        width = self.winfo_reqwidth()
        height = self.winfo_reqheight()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_widgets(self):
        """Setup login widgets"""
        # Main container
        main_frame = ctk.CTkFrame(self, corner_radius=20)
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)
        
        # Header
        header_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        header_frame.pack(fill="x", pady=(30, 20))
        
        # App icon
        icon_label = ctk.CTkLabel(
            header_frame,
            text="🏢",
            font=ctk.CTkFont(size=48)
        )
        icon_label.pack(pady=(0, 10))
        
        # App title
        title_label = ctk.CTkLabel(
            header_frame,
            text="نظام إدارة الأعمال",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack()
        
        # Subtitle
        subtitle_label = ctk.CTkLabel(
            header_frame,
            text="Integrated Business Management System",
            font=ctk.CTkFont(size=14)
        )
        subtitle_label.pack(pady=(5, 0))
        
        # Welcome message
        welcome_label = ctk.CTkLabel(
            header_frame,
            text="👋 مرحباً بك - Welcome",
            font=ctk.CTkFont(size=16)
        )
        welcome_label.pack(pady=(15, 0))
        
        # Form section
        form_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        form_frame.pack(fill="x", padx=30, pady=20)
        
        # Username field
        username_label = ctk.CTkLabel(
            form_frame,
            text="👤 اسم المستخدم - Username",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        username_label.pack(anchor="w", pady=(0, 8))
        
        self.username_entry = ctk.CTkEntry(
            form_frame,
            textvariable=self.username_var,
            placeholder_text="أدخل اسم المستخدم",
            height=45,
            font=ctk.CTkFont(size=14),
            corner_radius=12
        )
        self.username_entry.pack(fill="x", pady=(0, 15))
        
        # Password field
        password_label = ctk.CTkLabel(
            form_frame,
            text="🔒 كلمة المرور - Password",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        password_label.pack(anchor="w", pady=(0, 8))
        
        self.password_entry = ctk.CTkEntry(
            form_frame,
            textvariable=self.password_var,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            height=45,
            font=ctk.CTkFont(size=14),
            corner_radius=12
        )
        self.password_entry.pack(fill="x", pady=(0, 15))
        
        # Remember me checkbox
        self.remember_checkbox = ctk.CTkCheckBox(
            form_frame,
            text="💾 تذكرني - Remember Me",
            variable=self.remember_var,
            font=ctk.CTkFont(size=12)
        )
        self.remember_checkbox.pack(anchor="w", pady=(0, 20))
        
        # Login button
        self.login_button = ctk.CTkButton(
            form_frame,
            text="🚀 تسجيل الدخول - Login",
            command=self.login,
            height=50,
            font=ctk.CTkFont(size=16, weight="bold"),
            corner_radius=15
        )
        self.login_button.pack(fill="x", pady=(0, 15))
        
        # Language selection
        lang_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        lang_frame.pack(fill="x", padx=30, pady=(0, 20))
        
        lang_label = ctk.CTkLabel(
            lang_frame,
            text="🌍 اللغة - Language",
            font=ctk.CTkFont(size=12)
        )
        lang_label.pack(pady=(0, 10))
        
        # Language buttons
        lang_buttons_frame = ctk.CTkFrame(lang_frame, fg_color="transparent")
        lang_buttons_frame.pack()
        
        ar_button = ctk.CTkButton(
            lang_buttons_frame,
            text="🇸🇦 العربية",
            width=100,
            height=35,
            command=lambda: self.change_language('ar')
        )
        ar_button.pack(side="left", padx=5)
        
        en_button = ctk.CTkButton(
            lang_buttons_frame,
            text="🇺🇸 English",
            width=100,
            height=35,
            command=lambda: self.change_language('en')
        )
        en_button.pack(side="right", padx=5)
        
        # Footer
        footer_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        footer_frame.pack(fill="x", pady=(0, 20))
        
        version_label = ctk.CTkLabel(
            footer_frame,
            text="📱 Version 2.0.0 | © 2024 ERP System",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        version_label.pack()
        
        # Login info
        info_frame = ctk.CTkFrame(main_frame, fg_color="#e3f2fd", corner_radius=10)
        info_frame.pack(fill="x", padx=30, pady=(0, 20))
        
        info_label = ctk.CTkLabel(
            info_frame,
            text="🔐 بيانات الدخول التجريبية:\nاسم المستخدم: admin\nكلمة المرور: admin123",
            font=ctk.CTkFont(size=12),
            justify="center"
        )
        info_label.pack(pady=15)
    
    def login(self):
        """Perform login"""
        username = self.username_var.get().strip()
        password = self.password_var.get()
        
        # Validate inputs
        if not username or not password:
            messagebox.showerror("خطأ - Error", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return False
        
        # Disable login button during authentication
        self.login_button.configure(state="disabled", text="جاري التحقق...")
        self.update()
        
        try:
            # Attempt authentication
            if auth_manager.login(username, password):
                # Show success message
                user_name = auth_manager.get_user_name()
                messagebox.showinfo("نجح الدخول", f"مرحباً {user_name}!")
                
                # Close login window and open main application
                self.destroy()
                self.open_main_application()
                return True
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                return False
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {str(e)}")
            return False
        
        finally:
            # Re-enable login button
            self.login_button.configure(state="normal", text="🚀 تسجيل الدخول - Login")
    
    def open_main_application(self):
        """Open main application window"""
        try:
            # Try enhanced dashboard first
            from gui.enhanced_dashboard import EnhancedDashboard
            print("🌟 Loading Enhanced Dashboard...")
            dashboard = EnhancedDashboard()
            dashboard.mainloop()
        except Exception as e:
            print(f"⚠️ Enhanced dashboard failed: {e}")
            try:
                # Fallback to standard dashboard
                from gui.dashboard import DashboardWindow
                print("📱 Loading Standard Dashboard...")
                dashboard = DashboardWindow()
                dashboard.mainloop()
            except Exception as e2:
                print(f"❌ Dashboard failed: {e2}")
                messagebox.showerror("خطأ", f"فشل في تحميل لوحة التحكم: {str(e2)}")
    
    def change_language(self, language):
        """Change application language"""
        try:
            from utils import set_language
            if set_language(language):
                messagebox.showinfo("تم", f"تم تغيير اللغة إلى {language}")
                # Recreate window with new language
                self.destroy()
                new_login = SimpleLoginWindow()
                new_login.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تغيير اللغة: {str(e)}")
    
    def on_closing(self):
        """Handle window closing"""
        self.destroy()

def main():
    """Main function"""
    try:
        print("🔐 Starting Simple Login Window...")
        
        # Setup database
        print("🗄️ Setting up database...")
        db = DatabaseManager()
        db.init_database()
        
        # Setup language
        setup_language()
        
        print("✅ All components loaded successfully!")
        print("\n🔐 Login Information:")
        print("   Username: admin")
        print("   Password: admin123")
        print("\n🚀 Opening login window...")
        
        # Create and show login window
        login_window = SimpleLoginWindow()
        login_window.mainloop()
        
        print("✅ Login window closed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
