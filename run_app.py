#!/usr/bin/env python3
"""
Simple launcher for the ERP application
"""
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from main import main
    
    if __name__ == "__main__":
        print("🚀 Starting ERP Application...")
        print("📋 Loading modules...")
        main()
        
except Exception as e:
    print(f"❌ Error starting application: {e}")
    print("\n📦 Please ensure all dependencies are installed:")
    print("pip install -r requirements.txt")
    input("\nPress Enter to exit...")
