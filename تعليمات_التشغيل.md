# تعليمات تشغيل نظام إدارة الأعمال المتكامل

## 🚀 كيفية تشغيل التطبيق

### الطريقة الأولى - التشغيل المباشر:
```bash
python main.py
```

### الطريقة الثانية - استخدام ملف التشغيل:
```bash
python run_app.py
```

## 🔐 بيانات الدخول الافتراضية

**اسم المستخدم:** `admin`  
**كلمة المرور:** `admin123`

> ⚠️ **مهم:** يُرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول لأول مرة.

## 📋 الوحدات المتاحة

### ✅ الوحدات المكتملة:
1. **👥 إدارة العملاء** - إدارة بيانات العملاء والتواصل معهم
2. **🏢 إدارة الموردين** - إدارة بيانات الموردين وشروط التعامل
3. **📦 إدارة المخزون** - إدارة المنتجات والمخزون وحركة البضائع
4. **💰 إدارة المبيعات** - إنشاء وإدارة طلبات البيع
5. **🏠 لوحة التحكم** - عرض المؤشرات الرئيسية والإحصائيات

### 🔄 الوحدات قيد التطوير:
6. **🛒 إدارة المشتريات** - إدارة طلبات الشراء من الموردين
7. **🧾 إدارة الفواتير** - إنشاء وإدارة الفواتير
8. **📊 الحسابات العامة** - النظام المحاسبي الشامل
9. **📈 التقارير المالية** - تقارير الأرباح والخسائر والميزانيات
10. **👨‍💼 إدارة الموظفين** - إدارة بيانات الموظفين والرواتب

## 🎯 الميزات الرئيسية

### 🔐 الأمان والصلاحيات:
- نظام تسجيل دخول آمن
- إدارة الصلاحيات حسب الأدوار
- تسجيل جميع العمليات (Audit Trail)
- حماية من محاولات الاختراق

### 🌐 الدعم متعدد اللغات:
- دعم كامل للغة العربية مع اتجاه النص من اليمين لليسار
- دعم اللغة الإنجليزية
- إمكانية التبديل بين اللغات

### 📊 لوحة التحكم التفاعلية:
- مؤشرات الأداء الرئيسية في الوقت الفعلي
- رسوم بيانية تفاعلية للمبيعات والمخزون
- تتبع الأنشطة الحديثة
- نظام الإشعارات

### 🏢 دعم تعدد الفروع:
- إمكانية إدارة عدة فروع أو شركات
- فصل البيانات حسب الفرع
- تقارير موحدة أو منفصلة

## 🛠️ المتطلبات التقنية

### متطلبات النظام:
- **نظام التشغيل:** Windows 10/11، Linux، أو macOS
- **Python:** الإصدار 3.8 أو أحدث
- **الذاكرة:** 4 جيجابايت RAM كحد أدنى
- **مساحة القرص:** 500 ميجابايت

### المكتبات المطلوبة:
```
customtkinter==5.2.0      # واجهة المستخدم الحديثة
Pillow==10.0.0            # معالجة الصور
matplotlib==3.7.2         # الرسوم البيانية
pandas==2.0.3             # معالجة البيانات
reportlab==4.0.4          # إنشاء ملفات PDF
openpyxl==3.1.2           # ملفات Excel
python-bidi==0.4.2        # دعم النص ثنائي الاتجاه
arabic-reshaper==3.0.0    # تنسيق النص العربي
bcrypt==4.0.1             # تشفير كلمات المرور
```

## 📖 كيفية الاستخدام

### 1. تسجيل الدخول:
- افتح التطبيق
- أدخل اسم المستخدم وكلمة المرور
- اختر اللغة المفضلة (عربي/إنجليزي)

### 2. استكشاف لوحة التحكم:
- اطلع على المؤشرات الرئيسية
- راجع الأنشطة الحديثة
- تحقق من الإشعارات

### 3. استخدام الوحدات:
- انقر على أي وحدة من قائمة "الإدارات المتاحة"
- استخدم أزرار الإضافة والتعديل والحذف
- استفد من خاصية البحث والتصفية

### 4. إدارة البيانات:
- **العملاء:** أضف بيانات العملاء الكاملة مع تفاصيل الاتصال
- **الموردين:** سجل بيانات الموردين وشروط التعامل
- **المنتجات:** أدخل تفاصيل المنتجات والأسعار والمخزون
- **المبيعات:** أنشئ طلبات البيع وتتبع حالتها

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### خطأ في تشغيل التطبيق:
```bash
# تأكد من تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
python main.py
```

#### مشكلة في عرض النص العربي:
- تأكد من تثبيت مكتبات دعم العربية:
```bash
pip install python-bidi arabic-reshaper
```

#### مشكلة في قاعدة البيانات:
- سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
- في حالة وجود مشاكل، احذف ملف `erp_database.db` وأعد التشغيل

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملف `README.md` للتفاصيل التقنية
3. راجع التعليقات في الكود للمطورين

### تقرير المشاكل:
- صف المشكلة بالتفصيل
- أرفق رسالة الخطأ إن وجدت
- اذكر نظام التشغيل وإصدار Python

## 🔮 التطويرات المستقبلية

### الميزات القادمة:
- **واجهة ويب:** نسخة تعمل عبر المتصفح
- **تطبيق الهاتف:** نسخة للهواتف الذكية
- **التكامل السحابي:** حفظ البيانات في السحابة
- **تقارير متقدمة:** تقارير تفاعلية مع ذكاء اصطناعي
- **API متقدم:** للتكامل مع الأنظمة الأخرى

---

**نظام إدارة الأعمال المتكامل** - صُمم خصيصاً للشركات العربية 🇸🇦
