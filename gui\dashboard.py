"""
Dashboard window for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import pandas as pd
from datetime import datetime, timedelta
from gui.base_window import BaseWindow
from gui.components import DataTable, show_message
from utils import get_text, is_rtl, format_arabic_text, NumberUtils, DateUtils
from config import Config
from auth import auth_manager
from database import DatabaseManager

class DashboardWindow(BaseWindow):
    """Main dashboard window"""

    def __init__(self):
        super().__init__(
            title=get_text('dashboard.title'),
            width=1400,
            height=900
        )

        self.db = DatabaseManager()

        # Check authentication
        if not auth_manager.is_authenticated():
            self.destroy()
            from gui.login_window import LoginWindow
            login = LoginWindow()
            login.mainloop()
            return

        # Setup dashboard
        self.setup_dashboard()
        self.load_dashboard_data()

        # Auto-refresh every 5 minutes
        self.after(300000, self.auto_refresh)

    def setup_dashboard(self):
        """Setup dashboard layout"""
        # Main container
        self.main_container = ctk.CTkFrame(self)
        self.main_container.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)

        # Configure grid weights
        self.main_container.grid_rowconfigure(3, weight=1)  # Charts row
        self.main_container.grid_rowconfigure(4, weight=1)  # Activities row
        self.main_container.grid_columnconfigure(1, weight=1)

        # Create sections
        self.create_header_section()
        self.create_navigation_menu()
        self.create_metrics_section()
        self.create_charts_section()
        self.create_quick_actions_section()
        self.create_recent_activities_section()
        self.create_notifications_section()

    def create_header_section(self):
        """Create header with welcome message and quick info"""
        header_frame = ctk.CTkFrame(self.main_container)
        header_frame.grid(row=0, column=0, columnspan=3, sticky="ew", padx=5, pady=5)
        header_frame.grid_columnconfigure(1, weight=1)

        # Welcome message
        user_name = auth_manager.get_user_name()
        welcome_text = f"{get_text('login.welcome')} {user_name}"
        if is_rtl():
            welcome_text = format_arabic_text(welcome_text)

        welcome_label = ctk.CTkLabel(
            header_frame,
            text=welcome_text,
            font=ctk.CTkFont(size=24, weight="bold")
        )
        welcome_label.grid(row=0, column=0, padx=20, pady=15, sticky="w")

        # Current date and time
        current_time = DateUtils.format_datetime(DateUtils.get_current_datetime())
        time_label = ctk.CTkLabel(
            header_frame,
            text=current_time,
            font=ctk.CTkFont(size=14)
        )
        time_label.grid(row=0, column=2, padx=20, pady=15, sticky="e")

        # Branch info
        branch = auth_manager.get_user_branch()
        if branch:
            branch_label = ctk.CTkLabel(
                header_frame,
                text=f"{get_text('common.branch')}: {branch['name']}",
                font=ctk.CTkFont(size=12)
            )
            branch_label.grid(row=1, column=0, padx=20, pady=(0, 15), sticky="w")

    def create_navigation_menu(self):
        """Create main navigation menu"""
        nav_frame = ctk.CTkFrame(self.main_container)
        nav_frame.grid(row=1, column=0, columnspan=3, sticky="ew", padx=5, pady=5)

        # Title
        nav_title = ctk.CTkLabel(
            nav_frame,
            text="الإدارات المتاحة - Available Modules",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        nav_title.pack(pady=10)

        # Modules grid
        modules_frame = ctk.CTkFrame(nav_frame)
        modules_frame.pack(fill="x", padx=20, pady=10)

        # Configure grid for 5 columns
        for i in range(5):
            modules_frame.grid_columnconfigure(i, weight=1)

        # Module buttons with icons and descriptions
        modules = [
            # Row 1
            ("👥", get_text('modules.customers'), "إدارة بيانات العملاء", self.open_customers),
            ("🏢", get_text('modules.suppliers'), "إدارة بيانات الموردين", self.open_suppliers),
            ("📦", get_text('modules.inventory'), "إدارة المخزون والمنتجات", self.open_inventory),
            ("💰", get_text('modules.sales'), "إدارة المبيعات والطلبات", self.open_sales),
            ("🛒", get_text('modules.purchasing'), "إدارة المشتريات", self.open_purchasing),

            # Row 2
            ("🧾", get_text('modules.invoices'), "إدارة الفواتير", self.open_invoices),
            ("📊", get_text('modules.accounting'), "الحسابات العامة", self.open_accounting),
            ("📈", get_text('modules.reports'), "التقارير المالية", self.open_reports),
            ("👨‍💼", get_text('modules.employees'), "إدارة الموظفين", self.open_employees),
            ("⚙️", get_text('modules.settings'), "إعدادات النظام", self.open_settings),
        ]

        for i, (icon, title, desc, command) in enumerate(modules):
            row = i // 5
            col = i % 5

            # Module card
            module_card = ctk.CTkFrame(modules_frame)
            module_card.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

            # Icon
            icon_label = ctk.CTkLabel(
                module_card,
                text=icon,
                font=ctk.CTkFont(size=32)
            )
            icon_label.pack(pady=(10, 5))

            # Title
            title_label = ctk.CTkLabel(
                module_card,
                text=title,
                font=ctk.CTkFont(size=12, weight="bold"),
                wraplength=120
            )
            title_label.pack(pady=(0, 5))

            # Description
            desc_label = ctk.CTkLabel(
                module_card,
                text=desc,
                font=ctk.CTkFont(size=10),
                text_color="gray",
                wraplength=120
            )
            desc_label.pack(pady=(0, 10))

            # Button
            module_button = ctk.CTkButton(
                module_card,
                text="فتح - Open",
                command=command,
                width=100,
                height=30
            )
            module_button.pack(pady=(0, 10))

    def create_metrics_section(self):
        """Create key metrics cards"""
        metrics_frame = ctk.CTkFrame(self.main_container)
        metrics_frame.grid(row=2, column=0, columnspan=3, sticky="ew", padx=5, pady=5)

        # Configure grid
        for i in range(4):
            metrics_frame.grid_columnconfigure(i, weight=1)

        # Metric cards
        self.metrics_cards = {}

        # Total Sales
        self.metrics_cards['sales'] = self.create_metric_card(
            metrics_frame, 0, 0,
            get_text('dashboard.total_sales'),
            "0.00",
            "💰",
            "#2E8B57"
        )

        # Pending Invoices
        self.metrics_cards['invoices'] = self.create_metric_card(
            metrics_frame, 0, 1,
            get_text('dashboard.pending_invoices'),
            "0",
            "📄",
            "#FF6B35"
        )

        # Low Stock Items
        self.metrics_cards['stock'] = self.create_metric_card(
            metrics_frame, 0, 2,
            get_text('dashboard.low_stock'),
            "0",
            "📦",
            "#FF4444"
        )

        # Active Users
        self.metrics_cards['users'] = self.create_metric_card(
            metrics_frame, 0, 3,
            get_text('dashboard.active_users'),
            "0",
            "👥",
            "#4A90E2"
        )

    def create_metric_card(self, parent, row, col, title, value, icon, color):
        """Create a metric card"""
        card_frame = ctk.CTkFrame(parent)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky="ew")

        # Icon
        icon_label = ctk.CTkLabel(
            card_frame,
            text=icon,
            font=ctk.CTkFont(size=32)
        )
        icon_label.grid(row=0, column=0, padx=15, pady=15, sticky="w")

        # Content frame
        content_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        content_frame.grid(row=0, column=1, padx=15, pady=15, sticky="ew")

        # Title
        title_label = ctk.CTkLabel(
            content_frame,
            text=title,
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        title_label.pack(anchor="w" if not is_rtl() else "e")

        # Value
        value_label = ctk.CTkLabel(
            content_frame,
            text=value,
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=color
        )
        value_label.pack(anchor="w" if not is_rtl() else "e")

        card_frame.grid_columnconfigure(1, weight=1)

        return {
            'frame': card_frame,
            'value_label': value_label,
            'title_label': title_label
        }

    def create_charts_section(self):
        """Create charts section"""
        charts_frame = ctk.CTkFrame(self.main_container)
        charts_frame.grid(row=3, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)
        charts_frame.grid_rowconfigure(0, weight=1)
        charts_frame.grid_columnconfigure(0, weight=1)
        charts_frame.grid_columnconfigure(1, weight=1)

        # Sales chart
        self.create_sales_chart(charts_frame)

        # Inventory chart
        self.create_inventory_chart(charts_frame)

    def create_sales_chart(self, parent):
        """Create sales trend chart"""
        sales_frame = ctk.CTkFrame(parent)
        sales_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        # Title
        title_label = ctk.CTkLabel(
            sales_frame,
            text=get_text('dashboard.sales_trend'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)

        # Chart frame
        chart_frame = ctk.CTkFrame(sales_frame)
        chart_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create matplotlib figure
        self.sales_figure = Figure(figsize=(6, 4), dpi=100)
        self.sales_plot = self.sales_figure.add_subplot(111)

        # Canvas
        self.sales_canvas = FigureCanvasTkAgg(self.sales_figure, chart_frame)
        self.sales_canvas.get_tk_widget().pack(fill="both", expand=True)

    def create_inventory_chart(self, parent):
        """Create inventory status chart"""
        inventory_frame = ctk.CTkFrame(parent)
        inventory_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        # Title
        title_label = ctk.CTkLabel(
            inventory_frame,
            text=get_text('dashboard.inventory_status'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)

        # Chart frame
        chart_frame = ctk.CTkFrame(inventory_frame)
        chart_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create matplotlib figure
        self.inventory_figure = Figure(figsize=(6, 4), dpi=100)
        self.inventory_plot = self.inventory_figure.add_subplot(111)

        # Canvas
        self.inventory_canvas = FigureCanvasTkAgg(self.inventory_figure, chart_frame)
        self.inventory_canvas.get_tk_widget().pack(fill="both", expand=True)

    def create_quick_actions_section(self):
        """Create quick actions panel"""
        actions_frame = ctk.CTkFrame(self.main_container)
        actions_frame.grid(row=3, column=2, sticky="nsew", padx=5, pady=5)

        # Title
        title_label = ctk.CTkLabel(
            actions_frame,
            text=get_text('dashboard.quick_actions'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=15)

        # Action buttons - All available modules
        actions = [
            (get_text('modules.customers'), "👥", self.open_customers),
            (get_text('modules.suppliers'), "🏢", self.open_suppliers),
            (get_text('modules.inventory'), "📦", self.open_inventory),
            (get_text('modules.sales'), "💰", self.open_sales),
            (get_text('modules.purchasing'), "🛒", self.open_purchasing),
            (get_text('modules.invoices'), "🧾", self.open_invoices),
            (get_text('modules.accounting'), "📊", self.open_accounting),
            (get_text('modules.reports'), "📈", self.open_reports),
            (get_text('modules.employees'), "👨‍💼", self.open_employees),
            (get_text('modules.settings'), "⚙️", self.open_settings)
        ]

        # Create scrollable frame for buttons
        scrollable_frame = ctk.CTkScrollableFrame(actions_frame)
        scrollable_frame.pack(fill="both", expand=True, padx=15, pady=10)

        for text, icon, command in actions:
            button = ctk.CTkButton(
                scrollable_frame,
                text=f"{icon} {text}",
                command=command,
                height=40,
                anchor="w"
            )
            button.pack(fill="x", pady=3)

    def create_recent_activities_section(self):
        """Create recent activities section"""
        activities_frame = ctk.CTkFrame(self.main_container)
        activities_frame.grid(row=4, column=0, columnspan=2, sticky="nsew", padx=5, pady=5)
        activities_frame.grid_rowconfigure(1, weight=1)
        activities_frame.grid_columnconfigure(0, weight=1)

        # Title
        title_label = ctk.CTkLabel(
            activities_frame,
            text=get_text('dashboard.recent_activities'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=15)

        # Activities table
        columns = {
            'time': {'text': get_text('common.time'), 'width': 150},
            'user': {'text': get_text('common.user'), 'width': 120},
            'action': {'text': get_text('common.action'), 'width': 200},
            'details': {'text': get_text('common.details'), 'width': 300}
        }

        self.activities_table = DataTable(activities_frame, columns)
        self.activities_table.grid(row=1, column=0, sticky="nsew", padx=10, pady=10)

    def create_notifications_section(self):
        """Create notifications section"""
        notifications_frame = ctk.CTkFrame(self.main_container)
        notifications_frame.grid(row=4, column=2, sticky="nsew", padx=5, pady=5)
        notifications_frame.grid_rowconfigure(1, weight=1)
        notifications_frame.grid_columnconfigure(0, weight=1)

        # Title with count
        self.notifications_title = ctk.CTkLabel(
            notifications_frame,
            text=get_text('dashboard.notifications'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.notifications_title.pack(pady=15)

        # Notifications list
        self.notifications_frame = ctk.CTkScrollableFrame(notifications_frame)
        self.notifications_frame.pack(fill="both", expand=True, padx=10, pady=10)

    def load_dashboard_data(self):
        """Load dashboard data"""
        try:
            # Get dashboard metrics
            branch_id = auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None
            dashboard_data = self.db.get_dashboard_data(branch_id)

            # Update metric cards
            self.update_metric_card('sales', NumberUtils.format_currency(dashboard_data['total_sales']))
            self.update_metric_card('invoices', str(dashboard_data['pending_invoices']))
            self.update_metric_card('stock', str(dashboard_data['low_stock_items']))
            self.update_metric_card('users', str(dashboard_data['active_users']))

            # Load charts data
            self.load_sales_chart_data()
            self.load_inventory_chart_data()

            # Load recent activities
            self.load_recent_activities()

            # Load notifications
            self.load_notifications()

            self.set_status(get_text('common.data_loaded'))

        except Exception as e:
            self.show_error(f"Error loading dashboard data: {str(e)}")

    def update_metric_card(self, card_name, value):
        """Update metric card value"""
        if card_name in self.metrics_cards:
            self.metrics_cards[card_name]['value_label'].configure(text=value)

    def load_sales_chart_data(self):
        """Load sales chart data"""
        try:
            # Get sales data for last 30 days
            conn = self.db.get_connection()
            cursor = conn.cursor()

            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])

            cursor.execute(f'''
                SELECT DATE(order_date) as date, SUM(total_amount) as total
                FROM sales_orders
                {branch_filter}
                AND order_date >= date('now', '-30 days')
                GROUP BY DATE(order_date)
                ORDER BY date
            ''', params)

            data = cursor.fetchall()
            conn.close()

            if data:
                dates = [row['date'] for row in data]
                amounts = [row['total'] for row in data]

                self.sales_plot.clear()
                self.sales_plot.plot(dates, amounts, marker='o', linewidth=2, markersize=4)
                self.sales_plot.set_title(get_text('dashboard.sales_trend'))
                self.sales_plot.set_xlabel(get_text('common.date'))
                self.sales_plot.set_ylabel(get_text('common.amount'))
                self.sales_plot.tick_params(axis='x', rotation=45)
                self.sales_plot.grid(True, alpha=0.3)

                self.sales_figure.tight_layout()
                self.sales_canvas.draw()

        except Exception as e:
            print(f"Error loading sales chart: {e}")

    def load_inventory_chart_data(self):
        """Load inventory chart data"""
        try:
            # Get inventory status data
            conn = self.db.get_connection()
            cursor = conn.cursor()

            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])

            cursor.execute(f'''
                SELECT
                    CASE
                        WHEN current_stock <= 0 THEN 'Out of Stock'
                        WHEN current_stock <= minimum_stock THEN 'Low Stock'
                        WHEN current_stock >= maximum_stock THEN 'Overstock'
                        ELSE 'Normal'
                    END as status,
                    COUNT(*) as count
                FROM products
                {branch_filter}
                AND is_active = 1
                GROUP BY status
            ''', params)

            data = cursor.fetchall()
            conn.close()

            if data:
                labels = [row['status'] for row in data]
                sizes = [row['count'] for row in data]
                colors = ['#FF4444', '#FF6B35', '#FFD700', '#2E8B57']

                self.inventory_plot.clear()
                self.inventory_plot.pie(sizes, labels=labels, colors=colors[:len(sizes)],
                                      autopct='%1.1f%%', startangle=90)
                self.inventory_plot.set_title(get_text('dashboard.inventory_status'))

                self.inventory_canvas.draw()

        except Exception as e:
            print(f"Error loading inventory chart: {e}")

    def load_recent_activities(self):
        """Load recent activities"""
        try:
            # Get recent audit log entries
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT a.*, u.full_name
                FROM audit_log a
                LEFT JOIN users u ON a.user_id = u.id
                ORDER BY a.created_at DESC
                LIMIT 20
            ''')

            activities = []
            for row in cursor.fetchall():
                activities.append({
                    'time': DateUtils.format_datetime(row['created_at']),
                    'user': row['full_name'] or 'System',
                    'action': row['action'],
                    'details': f"{row['table_name']} #{row['record_id']}" if row['table_name'] else ""
                })

            self.activities_table.load_data(activities)
            conn.close()

        except Exception as e:
            print(f"Error loading activities: {e}")

    def load_notifications(self):
        """Load user notifications"""
        try:
            notifications = auth_manager.get_user_notifications(10)

            # Clear existing notifications
            for widget in self.notifications_frame.winfo_children():
                widget.destroy()

            # Update title with count
            unread_count = auth_manager.get_unread_notification_count()
            title_text = f"{get_text('dashboard.notifications')} ({unread_count})"
            self.notifications_title.configure(text=title_text)

            # Add notifications
            for notification in notifications:
                self.create_notification_widget(notification)

        except Exception as e:
            print(f"Error loading notifications: {e}")

    def create_notification_widget(self, notification):
        """Create notification widget"""
        # Notification frame
        notif_frame = ctk.CTkFrame(self.notifications_frame)
        notif_frame.pack(fill="x", padx=5, pady=2)

        # Notification content
        title_label = ctk.CTkLabel(
            notif_frame,
            text=notification['title'],
            font=ctk.CTkFont(weight="bold"),
            anchor="w"
        )
        title_label.pack(fill="x", padx=10, pady=(5, 0))

        message_label = ctk.CTkLabel(
            notif_frame,
            text=notification['message'],
            anchor="w",
            wraplength=200
        )
        message_label.pack(fill="x", padx=10, pady=(0, 5))

        # Time
        time_label = ctk.CTkLabel(
            notif_frame,
            text=DateUtils.format_datetime(notification['created_at']),
            font=ctk.CTkFont(size=10),
            text_color="gray",
            anchor="w"
        )
        time_label.pack(fill="x", padx=10, pady=(0, 5))

        # Mark as read if clicked
        if not notification['is_read']:
            notif_frame.configure(fg_color="#E3F2FD")
            notif_frame.bind("<Button-1>",
                           lambda e: self.mark_notification_read(notification['id']))

    def mark_notification_read(self, notification_id):
        """Mark notification as read"""
        auth_manager.mark_notification_read(notification_id)
        self.load_notifications()

    def auto_refresh(self):
        """Auto refresh dashboard data"""
        self.load_dashboard_data()
        # Schedule next refresh
        self.after(300000, self.auto_refresh)

    def refresh_data(self):
        """Refresh dashboard data"""
        self.load_dashboard_data()

    # Quick action methods
    def open_sales(self):
        """Open sales module"""
        try:
            from modules.sales import SalesWindow
            sales_window = SalesWindow(self)
            sales_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_inventory(self):
        """Open inventory module"""
        try:
            from modules.inventory import InventoryWindow
            inventory_window = InventoryWindow(self)
            inventory_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_customers(self):
        """Open customers module"""
        try:
            from modules.customers import CustomersWindow
            customers_window = CustomersWindow(self)
            customers_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_suppliers(self):
        """Open suppliers module"""
        try:
            from modules.suppliers import SuppliersWindow
            suppliers_window = SuppliersWindow(self)
            suppliers_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_reports(self):
        """Open reports module"""
        try:
            from modules.reports import ReportsWindow
            reports_window = ReportsWindow(self)
            reports_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_purchasing(self):
        """Open purchasing module"""
        try:
            from modules.purchasing import PurchasingWindow
            purchasing_window = PurchasingWindow(self)
            purchasing_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_invoices(self):
        """Open invoices module"""
        try:
            from modules.invoices import InvoicesWindow
            invoices_window = InvoicesWindow(self)
            invoices_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_accounting(self):
        """Open accounting module"""
        try:
            from modules.accounting import AccountingWindow
            accounting_window = AccountingWindow(self)
            accounting_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_employees(self):
        """Open employees module"""
        try:
            from modules.employees import EmployeesWindow
            employees_window = EmployeesWindow(self)
            employees_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_settings(self):
        """Open settings module"""
        try:
            from modules.settings import SettingsWindow
            settings_window = SettingsWindow(self)
            settings_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

if __name__ == "__main__":
    # Test dashboard
    dashboard = DashboardWindow()
    dashboard.mainloop()
