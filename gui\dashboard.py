"""
Dashboard window for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import ttk
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import pandas as pd
from datetime import datetime, timedelta
from gui.base_window import BaseWindow
from gui.components import DataTable, show_message
from utils import get_text, is_rtl, format_arabic_text, NumberUtils, DateUtils
from config import Config
from auth import auth_manager
from database import DatabaseManager

# تحسين الألوان والتصميم
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

# ألوان مخصصة للتصميم الاحترافي
COLORS = {
    'primary': '#1f538d',      # أزرق داكن احترافي
    'secondary': '#14a085',    # أخضر مائي
    'accent': '#f39c12',       # برتقالي ذهبي
    'success': '#27ae60',      # أخضر نجاح
    'warning': '#f39c12',      # برتقالي تحذير
    'danger': '#e74c3c',       # أحمر خطر
    'info': '#3498db',         # أزرق معلومات
    'light': '#ecf0f1',        # رمادي فاتح
    'dark': '#2c3e50',         # رمادي داكن
    'white': '#ffffff',        # أبيض
    'gradient_start': '#667eea', # بداية التدرج
    'gradient_end': '#764ba2',   # نهاية التدرج
    'card_bg': '#ffffff',      # خلفية البطاقات
    'shadow': '#00000020',     # ظل خفيف
    'border': '#e1e8ed',       # حدود
    'text_primary': '#2c3e50', # نص أساسي
    'text_secondary': '#6c757d' # نص ثانوي
}



class DashboardWindow(BaseWindow):
    """Main dashboard window with professional design"""

    def __init__(self):
        super().__init__(
            title=get_text('dashboard.title'),
            width=1600,  # عرض أكبر للتصميم الاحترافي
            height=1000   # ارتفاع أكبر
        )

        # تطبيق التصميم الاحترافي
        self.configure(fg_color=COLORS['light'])

        # إعداد الخطوط المخصصة
        self.setup_fonts()

        self.db = DatabaseManager()

    def setup_fonts(self):
        """إعداد الخطوط الاحترافية"""
        self.fonts = {
            'title': ctk.CTkFont(size=32, weight="bold"),
            'heading': ctk.CTkFont(size=24, weight="bold"),
            'subheading': ctk.CTkFont(size=18, weight="bold"),
            'body': ctk.CTkFont(size=14),
            'small': ctk.CTkFont(size=12),
            'caption': ctk.CTkFont(size=10),
            'button': ctk.CTkFont(size=14, weight="bold"),
            'metric': ctk.CTkFont(size=28, weight="bold"),
            'metric_label': ctk.CTkFont(size=12, weight="normal")
        }

        # Check authentication
        if not auth_manager.is_authenticated():
            self.destroy()
            from gui.login_window import LoginWindow
            login = LoginWindow()
            login.mainloop()
            return

        # Setup dashboard
        self.setup_dashboard()
        self.load_dashboard_data()

        # Auto-refresh every 5 minutes
        self.after(300000, self.auto_refresh)

    def setup_dashboard(self):
        """Setup dashboard layout"""
        # Main container
        self.main_container = ctk.CTkFrame(self)
        self.main_container.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)

        # Configure grid weights
        self.main_container.grid_rowconfigure(3, weight=1)  # Charts row
        self.main_container.grid_rowconfigure(4, weight=1)  # Activities row
        self.main_container.grid_columnconfigure(1, weight=1)

        # Create sections
        self.create_header_section()
        self.create_navigation_menu()
        self.create_metrics_section()
        self.create_charts_section()
        self.create_quick_actions_section()
        self.create_recent_activities_section()
        self.create_notifications_section()

    def create_header_section(self):
        """Create professional header with gradient background"""
        # Header container with gradient effect
        header_frame = ctk.CTkFrame(
            self.main_container,
            fg_color=COLORS['primary'],
            corner_radius=15,
            height=120
        )
        header_frame.grid(row=0, column=0, columnspan=3, sticky="ew", padx=10, pady=10)
        header_frame.grid_columnconfigure(1, weight=1)
        header_frame.grid_propagate(False)

        # Left section - Welcome and user info
        left_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        left_frame.grid(row=0, column=0, sticky="nsw", padx=20, pady=15)

        # Welcome message with icon
        user_name = auth_manager.get_user_name()
        welcome_text = f"👋 {get_text('login.welcome')} {user_name}"
        if is_rtl():
            welcome_text = format_arabic_text(welcome_text)

        welcome_label = ctk.CTkLabel(
            left_frame,
            text=welcome_text,
            font=self.fonts['heading'],
            text_color=COLORS['white']
        )
        welcome_label.pack(anchor="w", pady=(0, 5))

        # User role and branch info
        role_text = f"👤 {get_text('common.role')}: {auth_manager.get_user_role()}"
        branch = auth_manager.get_user_branch()
        if branch:
            role_text += f" | 🏢 {get_text('common.branch')}: {branch['name']}"

        info_label = ctk.CTkLabel(
            left_frame,
            text=role_text,
            font=self.fonts['small'],
            text_color=COLORS['light']
        )
        info_label.pack(anchor="w")

        # Center section - App title
        center_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        center_frame.grid(row=0, column=1, sticky="ns", padx=20, pady=15)

        app_title = ctk.CTkLabel(
            center_frame,
            text="🏢 نظام إدارة الأعمال",
            font=self.fonts['title'],
            text_color=COLORS['white']
        )
        app_title.pack(expand=True)

        # Right section - Date, time and notifications
        right_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        right_frame.grid(row=0, column=2, sticky="nse", padx=20, pady=15)

        # Current date and time with icon
        current_time = DateUtils.format_datetime(DateUtils.get_current_datetime())
        time_label = ctk.CTkLabel(
            right_frame,
            text=f"🕰️ {current_time}",
            font=self.fonts['small'],
            text_color=COLORS['white']
        )
        time_label.pack(anchor="e", pady=(0, 5))

        # Notifications button
        notifications_count = auth_manager.get_unread_notification_count()
        notif_text = f"🔔 الإشعارات ({notifications_count})"

        notif_button = ctk.CTkButton(
            right_frame,
            text=notif_text,
            font=self.fonts['small'],
            fg_color=COLORS['accent'],
            hover_color=COLORS['warning'],
            width=120,
            height=30,
            command=self.show_notifications
        )
        notif_button.pack(anchor="e")

    def show_notifications(self):
        """عرض نافذة الإشعارات"""
        # TODO: تنفيذ نافذة الإشعارات
        show_message(get_text('common.info'), "سيتم تطوير نظام الإشعارات قريباً", "info")

    def create_navigation_menu(self):
        """Create professional navigation menu with cards"""
        nav_frame = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        nav_frame.grid(row=1, column=0, columnspan=3, sticky="ew", padx=10, pady=5)

        # Title with modern styling
        title_frame = ctk.CTkFrame(nav_frame, fg_color="transparent")
        title_frame.pack(fill="x", pady=(0, 20))

        nav_title = ctk.CTkLabel(
            title_frame,
            text="📊 الإدارات المتاحة - Available Modules",
            font=self.fonts['heading'],
            text_color=COLORS['text_primary']
        )
        nav_title.pack()

        # Modules container with shadow effect
        modules_container = ctk.CTkFrame(
            nav_frame,
            fg_color=COLORS['white'],
            corner_radius=20
        )
        modules_container.pack(fill="x", padx=20, pady=10)

        # Modules grid
        modules_frame = ctk.CTkFrame(modules_container, fg_color="transparent")
        modules_frame.pack(fill="x", padx=20, pady=20)

        # Configure grid for 5 columns
        for i in range(5):
            modules_frame.grid_columnconfigure(i, weight=1)

        # Module data with status indicators
        modules = [
            # Row 1 - Core modules (completed)
            ("👥", get_text('modules.customers'), "إدارة بيانات العملاء", self.open_customers, True),
            ("🏢", get_text('modules.suppliers'), "إدارة بيانات الموردين", self.open_suppliers, True),
            ("📦", get_text('modules.inventory'), "إدارة المخزون والمنتجات", self.open_inventory, True),
            ("💰", get_text('modules.sales'), "إدارة المبيعات والطلبات", self.open_sales, True),
            ("🛒", get_text('modules.purchasing'), "إدارة المشتريات", self.open_purchasing, False),

            # Row 2 - Additional modules (in development)
            ("🧾", get_text('modules.invoices'), "إدارة الفواتير", self.open_invoices, False),
            ("📊", get_text('modules.accounting'), "الحسابات العامة", self.open_accounting, False),
            ("📈", get_text('modules.reports'), "التقارير المالية", self.open_reports, False),
            ("👨‍💼", get_text('modules.employees'), "إدارة الموظفين", self.open_employees, False),
            ("⚙️", get_text('modules.settings'), "إعدادات النظام", self.open_settings, False),
        ]

        for i, (icon, title, desc, command, is_ready) in enumerate(modules):
            row = i // 5
            col = i % 5

            # Module card with hover effect
            card_color = COLORS['card_bg'] if is_ready else COLORS['light']
            module_card = ctk.CTkFrame(
                modules_frame,
                fg_color=card_color,
                corner_radius=15,
                border_width=2,
                border_color=COLORS['primary'] if is_ready else COLORS['text_secondary']
            )
            module_card.grid(row=row, column=col, padx=8, pady=8, sticky="nsew")

            # Status indicator
            status_frame = ctk.CTkFrame(module_card, fg_color="transparent")
            status_frame.pack(fill="x", padx=10, pady=(10, 0))

            status_indicator = ctk.CTkLabel(
                status_frame,
                text="✅ جاهز" if is_ready else "🔄 قيد التطوير",
                font=self.fonts['caption'],
                text_color=COLORS['success'] if is_ready else COLORS['warning']
            )
            status_indicator.pack(side="right")

            # Icon with animation effect
            icon_label = ctk.CTkLabel(
                module_card,
                text=icon,
                font=ctk.CTkFont(size=40)
            )
            icon_label.pack(pady=(15, 10))

            # Title
            title_label = ctk.CTkLabel(
                module_card,
                text=title,
                font=self.fonts['subheading'],
                text_color=COLORS['text_primary'],
                wraplength=140
            )
            title_label.pack(pady=(0, 5))

            # Description
            desc_label = ctk.CTkLabel(
                module_card,
                text=desc,
                font=self.fonts['small'],
                text_color=COLORS['text_secondary'],
                wraplength=140
            )
            desc_label.pack(pady=(0, 15))

            # Action button with modern styling
            button_color = COLORS['primary'] if is_ready else COLORS['text_secondary']
            button_text = "🚀 فتح الوحدة" if is_ready else "🕰️ قريباً"

            module_button = ctk.CTkButton(
                module_card,
                text=button_text,
                command=command,
                font=self.fonts['button'],
                fg_color=button_color,
                hover_color=COLORS['secondary'] if is_ready else COLORS['text_secondary'],
                width=120,
                height=35,
                corner_radius=10
            )
            module_button.pack(pady=(0, 15))

            # Configure grid weights for responsive design
            modules_frame.grid_rowconfigure(row, weight=1)

    def create_metrics_section(self):
        """Create professional metrics cards with animations"""
        metrics_container = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        metrics_container.grid(row=2, column=0, columnspan=3, sticky="ew", padx=10, pady=10)

        # Metrics title
        metrics_title = ctk.CTkLabel(
            metrics_container,
            text="📈 مؤشرات الأداء الرئيسية - Key Performance Indicators",
            font=self.fonts['subheading'],
            text_color=COLORS['text_primary']
        )
        metrics_title.pack(pady=(0, 15))

        # Metrics frame with modern design
        metrics_frame = ctk.CTkFrame(
            metrics_container,
            fg_color=COLORS['white'],
            corner_radius=20
        )
        metrics_frame.pack(fill="x", padx=20)

        # Configure grid for responsive design
        for i in range(4):
            metrics_frame.grid_columnconfigure(i, weight=1)
        metrics_frame.grid_rowconfigure(0, weight=1)

        # Metric cards
        self.metrics_cards = {}

        # Modern metric cards with gradient effects
        metrics_data = [
            {
                'key': 'sales',
                'title': '💰 ' + get_text('dashboard.total_sales'),
                'value': '0.00',
                'color': COLORS['success'],
                'gradient': ['#27ae60', '#2ecc71'],
                'icon': '💰'
            },
            {
                'key': 'invoices',
                'title': '📄 ' + get_text('dashboard.pending_invoices'),
                'value': '0',
                'color': COLORS['warning'],
                'gradient': ['#f39c12', '#e67e22'],
                'icon': '📄'
            },
            {
                'key': 'stock',
                'title': '📦 ' + get_text('dashboard.low_stock'),
                'value': '0',
                'color': COLORS['danger'],
                'gradient': ['#e74c3c', '#c0392b'],
                'icon': '📦'
            },
            {
                'key': 'users',
                'title': '👥 ' + get_text('dashboard.active_users'),
                'value': '0',
                'color': COLORS['info'],
                'gradient': ['#3498db', '#2980b9'],
                'icon': '👥'
            }
        ]

        for i, metric in enumerate(metrics_data):
            self.metrics_cards[metric['key']] = self.create_modern_metric_card(
                metrics_frame, 0, i, metric
            )

    def create_modern_metric_card(self, parent, row, col, metric_data):
        """Create a modern metric card with gradient and animations"""
        # Card container with shadow effect
        card_container = ctk.CTkFrame(
            parent,
            fg_color="transparent"
        )
        card_container.grid(row=row, column=col, padx=15, pady=20, sticky="nsew")

        # Main card with gradient-like effect
        card_frame = ctk.CTkFrame(
            card_container,
            fg_color=COLORS['white'],
            corner_radius=20,
            border_width=3,
            border_color=metric_data['color']
        )
        card_frame.pack(fill="both", expand=True)

        # Header with colored background
        header_frame = ctk.CTkFrame(
            card_frame,
            fg_color=metric_data['color'],
            corner_radius=15,
            height=60
        )
        header_frame.pack(fill="x", padx=10, pady=(10, 0))
        header_frame.pack_propagate(False)

        # Icon in header
        icon_label = ctk.CTkLabel(
            header_frame,
            text=metric_data['icon'],
            font=ctk.CTkFont(size=28),
            text_color=COLORS['white']
        )
        icon_label.pack(side="left", padx=15, pady=15)

        # Title in header
        title_label = ctk.CTkLabel(
            header_frame,
            text=metric_data['title'],
            font=self.fonts['metric_label'],
            text_color=COLORS['white'],
            wraplength=150
        )
        title_label.pack(side="right", padx=15, pady=15)

        # Value section
        value_frame = ctk.CTkFrame(card_frame, fg_color="transparent")
        value_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Main value
        value_label = ctk.CTkLabel(
            value_frame,
            text=metric_data['value'],
            font=self.fonts['metric'],
            text_color=metric_data['color']
        )
        value_label.pack(expand=True)

        # Trend indicator (placeholder)
        trend_frame = ctk.CTkFrame(value_frame, fg_color="transparent")
        trend_frame.pack(fill="x", pady=(10, 0))

        trend_label = ctk.CTkLabel(
            trend_frame,
            text="📈 +12% من الشهر الماضي",
            font=self.fonts['caption'],
            text_color=COLORS['success']
        )
        trend_label.pack()

        return {
            'frame': card_frame,
            'value_label': value_label,
            'title_label': title_label,
            'container': card_container
        }

    def create_charts_section(self):
        """Create modern charts section with professional styling"""
        charts_container = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        charts_container.grid(row=3, column=0, columnspan=2, sticky="nsew", padx=10, pady=10)

        # Charts title
        charts_title = ctk.CTkLabel(
            charts_container,
            text="📉 الرسوم البيانية التفاعلية - Interactive Charts",
            font=self.fonts['subheading'],
            text_color=COLORS['text_primary']
        )
        charts_title.pack(pady=(0, 15))

        # Charts frame with modern design
        charts_frame = ctk.CTkFrame(
            charts_container,
            fg_color=COLORS['white'],
            corner_radius=20
        )
        charts_frame.pack(fill="both", expand=True, padx=20)
        charts_frame.grid_rowconfigure(0, weight=1)
        charts_frame.grid_columnconfigure(0, weight=1)
        charts_frame.grid_columnconfigure(1, weight=1)

        # Sales chart
        self.create_sales_chart(charts_frame)

        # Inventory chart
        self.create_inventory_chart(charts_frame)

    def create_sales_chart(self, parent):
        """Create sales trend chart"""
        sales_frame = ctk.CTkFrame(parent)
        sales_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")

        # Title
        title_label = ctk.CTkLabel(
            sales_frame,
            text=get_text('dashboard.sales_trend'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)

        # Chart frame
        chart_frame = ctk.CTkFrame(sales_frame)
        chart_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create matplotlib figure
        self.sales_figure = Figure(figsize=(6, 4), dpi=100)
        self.sales_plot = self.sales_figure.add_subplot(111)

        # Canvas
        self.sales_canvas = FigureCanvasTkAgg(self.sales_figure, chart_frame)
        self.sales_canvas.get_tk_widget().pack(fill="both", expand=True)

    def create_inventory_chart(self, parent):
        """Create inventory status chart"""
        inventory_frame = ctk.CTkFrame(parent)
        inventory_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")

        # Title
        title_label = ctk.CTkLabel(
            inventory_frame,
            text=get_text('dashboard.inventory_status'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)

        # Chart frame
        chart_frame = ctk.CTkFrame(inventory_frame)
        chart_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create matplotlib figure
        self.inventory_figure = Figure(figsize=(6, 4), dpi=100)
        self.inventory_plot = self.inventory_figure.add_subplot(111)

        # Canvas
        self.inventory_canvas = FigureCanvasTkAgg(self.inventory_figure, chart_frame)
        self.inventory_canvas.get_tk_widget().pack(fill="both", expand=True)

    def create_quick_actions_section(self):
        """Create modern quick actions sidebar"""
        actions_container = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        actions_container.grid(row=3, column=2, sticky="nsew", padx=10, pady=10)

        # Actions title
        actions_title = ctk.CTkLabel(
            actions_container,
            text="⚡ إجراءات سريعة - Quick Actions",
            font=self.fonts['subheading'],
            text_color=COLORS['text_primary']
        )
        actions_title.pack(pady=(0, 15))

        # Actions frame with modern design
        actions_frame = ctk.CTkFrame(
            actions_container,
            fg_color=COLORS['white'],
            corner_radius=20
        )
        actions_frame.pack(fill="both", expand=True, padx=20)

        # Quick actions data
        quick_actions = [
            {
                'text': get_text('modules.customers'),
                'icon': '👥',
                'command': self.open_customers,
                'color': COLORS['primary'],
                'ready': True
            },
            {
                'text': get_text('modules.sales'),
                'icon': '💰',
                'command': self.open_sales,
                'color': COLORS['success'],
                'ready': True
            },
            {
                'text': get_text('modules.inventory'),
                'icon': '📦',
                'command': self.open_inventory,
                'color': COLORS['info'],
                'ready': True
            },
            {
                'text': get_text('modules.suppliers'),
                'icon': '🏢',
                'command': self.open_suppliers,
                'color': COLORS['secondary'],
                'ready': True
            },
            {
                'text': get_text('modules.reports'),
                'icon': '📈',
                'command': self.open_reports,
                'color': COLORS['warning'],
                'ready': False
            },
            {
                'text': get_text('modules.settings'),
                'icon': '⚙️',
                'command': self.open_settings,
                'color': COLORS['text_secondary'],
                'ready': False
            }
        ]

        # Create scrollable frame for actions
        scrollable_frame = ctk.CTkScrollableFrame(
            actions_frame,
            fg_color="transparent"
        )
        scrollable_frame.pack(fill="both", expand=True, padx=20, pady=20)

        for action in quick_actions:
            # Action button with modern styling
            button_frame = ctk.CTkFrame(
                scrollable_frame,
                fg_color="transparent"
            )
            button_frame.pack(fill="x", pady=5)

            action_button = ctk.CTkButton(
                button_frame,
                text=f"{action['icon']} {action['text']}",
                command=action['command'],
                font=self.fonts['button'],
                fg_color=action['color'] if action['ready'] else COLORS['text_secondary'],
                hover_color=COLORS['secondary'] if action['ready'] else COLORS['text_secondary'],
                height=45,
                corner_radius=12,
                anchor="w"
            )
            action_button.pack(fill="x")

            # Add status indicator for non-ready modules
            if not action['ready']:
                status_label = ctk.CTkLabel(
                    button_frame,
                    text="🕰️ قيد التطوير",
                    font=self.fonts['caption'],
                    text_color=COLORS['text_secondary']
                )
                status_label.pack(pady=(2, 0))

    def create_recent_activities_section(self):
        """Create modern recent activities section"""
        activities_container = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        activities_container.grid(row=4, column=0, columnspan=2, sticky="nsew", padx=10, pady=10)

        # Activities title
        activities_title = ctk.CTkLabel(
            activities_container,
            text="📅 الأنشطة الحديثة - Recent Activities",
            font=self.fonts['subheading'],
            text_color=COLORS['text_primary']
        )
        activities_title.pack(pady=(0, 15))

        # Activities frame with modern design
        activities_frame = ctk.CTkFrame(
            activities_container,
            fg_color=COLORS['white'],
            corner_radius=20
        )
        activities_frame.pack(fill="both", expand=True, padx=20)
        activities_frame.grid_rowconfigure(1, weight=1)
        activities_frame.grid_columnconfigure(0, weight=1)

        # Title
        title_label = ctk.CTkLabel(
            activities_frame,
            text=get_text('dashboard.recent_activities'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=15)

        # Activities table
        columns = {
            'time': {'text': get_text('common.time'), 'width': 150},
            'user': {'text': get_text('common.user'), 'width': 120},
            'action': {'text': get_text('common.action'), 'width': 200},
            'details': {'text': get_text('common.details'), 'width': 300}
        }

        self.activities_table = DataTable(activities_frame, columns)
        self.activities_table.grid(row=1, column=0, sticky="nsew", padx=10, pady=10)

    def create_notifications_section(self):
        """Create modern notifications section"""
        notifications_container = ctk.CTkFrame(
            self.main_container,
            fg_color="transparent"
        )
        notifications_container.grid(row=4, column=2, sticky="nsew", padx=10, pady=10)

        # Notifications title with count
        self.notifications_title = ctk.CTkLabel(
            notifications_container,
            text="🔔 الإشعارات - Notifications",
            font=self.fonts['subheading'],
            text_color=COLORS['text_primary']
        )
        self.notifications_title.pack(pady=(0, 15))

        # Notifications frame with modern design
        notifications_frame = ctk.CTkFrame(
            notifications_container,
            fg_color=COLORS['white'],
            corner_radius=20
        )
        notifications_frame.pack(fill="both", expand=True, padx=20)

        # Notifications list
        self.notifications_frame = ctk.CTkScrollableFrame(
            notifications_frame,
            fg_color="transparent"
        )
        self.notifications_frame.pack(fill="both", expand=True, padx=20, pady=20)

    def load_dashboard_data(self):
        """Load dashboard data"""
        try:
            # Get dashboard metrics
            branch_id = auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None
            dashboard_data = self.db.get_dashboard_data(branch_id)

            # Update metric cards
            self.update_metric_card('sales', NumberUtils.format_currency(dashboard_data['total_sales']))
            self.update_metric_card('invoices', str(dashboard_data['pending_invoices']))
            self.update_metric_card('stock', str(dashboard_data['low_stock_items']))
            self.update_metric_card('users', str(dashboard_data['active_users']))

            # Load charts data
            self.load_sales_chart_data()
            self.load_inventory_chart_data()

            # Load recent activities
            self.load_recent_activities()

            # Load notifications
            self.load_notifications()

            self.set_status(get_text('common.data_loaded'))

        except Exception as e:
            self.show_error(f"Error loading dashboard data: {str(e)}")

    def update_metric_card(self, card_name, value):
        """Update metric card value with animation effect"""
        if card_name in self.metrics_cards:
            # Update the value with smooth transition
            value_label = self.metrics_cards[card_name]['value_label']
            value_label.configure(text=value)

            # Add a subtle animation effect (color change)
            original_color = value_label.cget('text_color')
            value_label.configure(text_color=COLORS['accent'])

            # Reset color after a short delay
            self.after(500, lambda: value_label.configure(text_color=original_color))

    def load_sales_chart_data(self):
        """Load sales chart data"""
        try:
            # Get sales data for last 30 days
            conn = self.db.get_connection()
            cursor = conn.cursor()

            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])

            cursor.execute(f'''
                SELECT DATE(order_date) as date, SUM(total_amount) as total
                FROM sales_orders
                {branch_filter}
                AND order_date >= date('now', '-30 days')
                GROUP BY DATE(order_date)
                ORDER BY date
            ''', params)

            data = cursor.fetchall()
            conn.close()

            if data:
                dates = [row['date'] for row in data]
                amounts = [row['total'] for row in data]

                self.sales_plot.clear()
                self.sales_plot.plot(dates, amounts, marker='o', linewidth=2, markersize=4)
                self.sales_plot.set_title(get_text('dashboard.sales_trend'))
                self.sales_plot.set_xlabel(get_text('common.date'))
                self.sales_plot.set_ylabel(get_text('common.amount'))
                self.sales_plot.tick_params(axis='x', rotation=45)
                self.sales_plot.grid(True, alpha=0.3)

                self.sales_figure.tight_layout()
                self.sales_canvas.draw()

        except Exception as e:
            print(f"Error loading sales chart: {e}")

    def load_inventory_chart_data(self):
        """Load inventory chart data"""
        try:
            # Get inventory status data
            conn = self.db.get_connection()
            cursor = conn.cursor()

            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])

            cursor.execute(f'''
                SELECT
                    CASE
                        WHEN current_stock <= 0 THEN 'Out of Stock'
                        WHEN current_stock <= minimum_stock THEN 'Low Stock'
                        WHEN current_stock >= maximum_stock THEN 'Overstock'
                        ELSE 'Normal'
                    END as status,
                    COUNT(*) as count
                FROM products
                {branch_filter}
                AND is_active = 1
                GROUP BY status
            ''', params)

            data = cursor.fetchall()
            conn.close()

            if data:
                labels = [row['status'] for row in data]
                sizes = [row['count'] for row in data]
                colors = ['#FF4444', '#FF6B35', '#FFD700', '#2E8B57']

                self.inventory_plot.clear()
                self.inventory_plot.pie(sizes, labels=labels, colors=colors[:len(sizes)],
                                      autopct='%1.1f%%', startangle=90)
                self.inventory_plot.set_title(get_text('dashboard.inventory_status'))

                self.inventory_canvas.draw()

        except Exception as e:
            print(f"Error loading inventory chart: {e}")

    def load_recent_activities(self):
        """Load recent activities"""
        try:
            # Get recent audit log entries
            conn = self.db.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT a.*, u.full_name
                FROM audit_log a
                LEFT JOIN users u ON a.user_id = u.id
                ORDER BY a.created_at DESC
                LIMIT 20
            ''')

            activities = []
            for row in cursor.fetchall():
                activities.append({
                    'time': DateUtils.format_datetime(row['created_at']),
                    'user': row['full_name'] or 'System',
                    'action': row['action'],
                    'details': f"{row['table_name']} #{row['record_id']}" if row['table_name'] else ""
                })

            self.activities_table.load_data(activities)
            conn.close()

        except Exception as e:
            print(f"Error loading activities: {e}")

    def load_notifications(self):
        """Load user notifications"""
        try:
            notifications = auth_manager.get_user_notifications(10)

            # Clear existing notifications
            for widget in self.notifications_frame.winfo_children():
                widget.destroy()

            # Update title with count
            unread_count = auth_manager.get_unread_notification_count()
            title_text = f"{get_text('dashboard.notifications')} ({unread_count})"
            self.notifications_title.configure(text=title_text)

            # Add notifications
            for notification in notifications:
                self.create_notification_widget(notification)

        except Exception as e:
            print(f"Error loading notifications: {e}")

    def create_notification_widget(self, notification):
        """Create modern notification widget"""
        # Notification card with modern styling
        notif_card = ctk.CTkFrame(
            self.notifications_frame,
            fg_color=COLORS['light'] if not notification['is_read'] else COLORS['white'],
            corner_radius=10,
            border_width=1,
            border_color=COLORS['primary'] if not notification['is_read'] else COLORS['border']
        )
        notif_card.pack(fill="x", padx=5, pady=5)

        # Header with icon and status
        header_frame = ctk.CTkFrame(notif_card, fg_color="transparent")
        header_frame.pack(fill="x", padx=15, pady=(10, 5))

        # Notification icon based on type
        icon_map = {
            'info': '📝',
            'warning': '⚠️',
            'success': '✅',
            'error': '❌'
        }
        icon = icon_map.get(notification.get('type', 'info'), '📝')

        icon_label = ctk.CTkLabel(
            header_frame,
            text=icon,
            font=ctk.CTkFont(size=16)
        )
        icon_label.pack(side="left", padx=(0, 10))

        # Status indicator
        if not notification['is_read']:
            status_label = ctk.CTkLabel(
                header_frame,
                text="• جديد",
                font=self.fonts['caption'],
                text_color=COLORS['primary']
            )
            status_label.pack(side="right")

        # Notification content
        content_frame = ctk.CTkFrame(notif_card, fg_color="transparent")
        content_frame.pack(fill="x", padx=15, pady=(0, 10))

        # Title
        title_label = ctk.CTkLabel(
            content_frame,
            text=notification['title'],
            font=self.fonts['small'],
            text_color=COLORS['text_primary'],
            anchor="w",
            wraplength=180
        )
        title_label.pack(fill="x", pady=(0, 5))

        # Message
        message_label = ctk.CTkLabel(
            content_frame,
            text=notification['message'],
            font=self.fonts['caption'],
            text_color=COLORS['text_secondary'],
            anchor="w",
            wraplength=180
        )
        message_label.pack(fill="x", pady=(0, 5))

        # Time with icon
        time_text = f"🕰️ {DateUtils.format_datetime(notification['created_at'])}"
        time_label = ctk.CTkLabel(
            content_frame,
            text=time_text,
            font=self.fonts['caption'],
            text_color=COLORS['text_secondary'],
            anchor="w"
        )
        time_label.pack(fill="x")

        # Click handler for marking as read
        if not notification['is_read']:
            def mark_read(event):
                self.mark_notification_read(notification['id'])

            notif_card.bind("<Button-1>", mark_read)
            # Bind to all child widgets too
            for widget in [header_frame, content_frame, title_label, message_label, time_label]:
                widget.bind("<Button-1>", mark_read)

    def mark_notification_read(self, notification_id):
        """Mark notification as read"""
        auth_manager.mark_notification_read(notification_id)
        self.load_notifications()

    def auto_refresh(self):
        """Auto refresh dashboard data"""
        self.load_dashboard_data()
        # Schedule next refresh
        self.after(300000, self.auto_refresh)

    def refresh_data(self):
        """Refresh dashboard data"""
        self.load_dashboard_data()

    # Quick action methods
    def open_sales(self):
        """Open sales module"""
        try:
            from modules.sales import SalesWindow
            sales_window = SalesWindow(self)
            sales_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_inventory(self):
        """Open inventory module"""
        try:
            from modules.inventory import InventoryWindow
            inventory_window = InventoryWindow(self)
            inventory_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_customers(self):
        """Open customers module"""
        try:
            from modules.customers import CustomersWindow
            customers_window = CustomersWindow(self)
            customers_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_suppliers(self):
        """Open suppliers module"""
        try:
            from modules.suppliers import SuppliersWindow
            suppliers_window = SuppliersWindow(self)
            suppliers_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_reports(self):
        """Open reports module"""
        try:
            from modules.reports import ReportsWindow
            reports_window = ReportsWindow(self)
            reports_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_purchasing(self):
        """Open purchasing module"""
        try:
            from modules.purchasing import PurchasingWindow
            purchasing_window = PurchasingWindow(self)
            purchasing_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_invoices(self):
        """Open invoices module"""
        try:
            from modules.invoices import InvoicesWindow
            invoices_window = InvoicesWindow(self)
            invoices_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_accounting(self):
        """Open accounting module"""
        try:
            from modules.accounting import AccountingWindow
            accounting_window = AccountingWindow(self)
            accounting_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_employees(self):
        """Open employees module"""
        try:
            from modules.employees import EmployeesWindow
            employees_window = EmployeesWindow(self)
            employees_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

    def open_settings(self):
        """Open settings module"""
        try:
            from modules.settings import SettingsWindow
            settings_window = SettingsWindow(self)
            settings_window.grab_set()
        except ImportError:
            show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")

if __name__ == "__main__":
    # Test dashboard
    dashboard = DashboardWindow()
    dashboard.mainloop()
