#!/usr/bin/env python3
"""
Quick Start Script for Enhanced ERP System
تشغيل سريع لنظام إدارة الأعمال المحسن
"""
import sys
import os
import subprocess
from pathlib import Path

def print_banner():
    """Print application banner"""
    print("🏢" + "=" * 68 + "🏢")
    print("🌟  Enhanced ERP System - نظام إدارة الأعمال المحسن  🌟")
    print("🏢" + "=" * 68 + "🏢")
    print()

def check_and_install_requirements():
    """Check and install missing requirements"""
    required_packages = {
        'customtkinter': 'customtkinter>=5.2.0',
        'PIL': 'Pillow>=9.0.0',
        'matplotlib': 'matplotlib>=3.5.0',
        'pandas': 'pandas>=1.4.0',
        'numpy': 'numpy>=1.21.0'
    }
    
    missing_packages = []
    
    print("📦 Checking required packages...")
    
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package} - OK")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join([p.split('>=')[0] for p in missing_packages])}")
        
        response = input("🤔 Do you want to install them now? (y/n): ").lower().strip()
        
        if response in ['y', 'yes', 'نعم', 'ن']:
            print("\n🔧 Installing missing packages...")
            
            for package in missing_packages:
                print(f"   Installing {package.split('>=')[0]}...")
                try:
                    result = subprocess.run([
                        sys.executable, "-m", "pip", "install", package
                    ], capture_output=True, text=True)
                    
                    if result.returncode == 0:
                        print(f"✅ {package.split('>=')[0]} installed successfully")
                    else:
                        print(f"❌ Failed to install {package.split('>=')[0]}")
                        print(f"   Error: {result.stderr}")
                        return False
                        
                except Exception as e:
                    print(f"❌ Error installing {package}: {e}")
                    return False
            
            print("✅ All packages installed successfully!")
        else:
            print("❌ Cannot continue without required packages")
            return False
    
    return True

def setup_database():
    """Setup database if needed"""
    db_file = Path("erp_database.db")
    
    if not db_file.exists():
        print("\n🗄️ Database not found. Setting up...")
        
        try:
            # Try to import and setup database
            from database import DatabaseManager
            db = DatabaseManager()
            db.init_database()
            print("✅ Database setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Error setting up database: {e}")
            
            # Try alternative setup
            try:
                print("🔄 Trying alternative setup...")
                if Path("setup_enhanced.py").exists():
                    subprocess.run([sys.executable, "setup_enhanced.py"])
                elif Path("setup.py").exists():
                    subprocess.run([sys.executable, "setup.py"])
                else:
                    print("⚠️ No setup script found. Creating basic database...")
                    # Create basic database
                    import sqlite3
                    conn = sqlite3.connect("erp_database.db")
                    conn.execute("CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY)")
                    conn.commit()
                    conn.close()
                    print("✅ Basic database created")
                
                return True
                
            except Exception as e2:
                print(f"❌ Alternative setup failed: {e2}")
                return False
    else:
        print("✅ Database found")
        return True

def find_and_run_app():
    """Find and run the best available version of the app"""
    print("\n🚀 Starting application...")
    
    # Priority order of application files
    app_files = [
        ("run_enhanced_app.py", "🌟 Enhanced Version"),
        ("main.py", "📱 Standard Version"),
        ("run_app.py", "🔄 Alternative Version"),
        ("start_erp.py", "⚡ Quick Start Version")
    ]
    
    for app_file, description in app_files:
        if Path(app_file).exists():
            print(f"✅ Found {description}: {app_file}")
            
            try:
                print(f"🎯 Launching {description}...")
                
                # Import and run the application
                if app_file == "run_enhanced_app.py":
                    # Run enhanced version
                    exec(open(app_file).read())
                elif app_file == "main.py":
                    # Run standard version
                    exec(open(app_file).read())
                else:
                    # Run other versions
                    subprocess.run([sys.executable, app_file])
                
                return True
                
            except Exception as e:
                print(f"❌ Error running {app_file}: {e}")
                print("🔄 Trying next available version...")
                continue
    
    print("❌ No runnable application file found!")
    print("💡 Please make sure you have the application files in this directory")
    return False

def show_help():
    """Show help information"""
    print("\n📚 Quick Help:")
    print("=" * 50)
    print("🔐 Default Login:")
    print("   Username: admin")
    print("   Password: admin123")
    print()
    print("🎨 Features:")
    print("   • Modern professional interface")
    print("   • Multiple themes and languages")
    print("   • Interactive charts and metrics")
    print("   • Customer and supplier management")
    print("   • Inventory and sales tracking")
    print()
    print("🆘 Troubleshooting:")
    print("   • If app doesn't start, run: python setup_enhanced.py")
    print("   • For missing packages, run: pip install -r requirements.txt")
    print("   • Check README_ENHANCED.md for detailed help")
    print("=" * 50)

def main():
    """Main function"""
    try:
        print_banner()
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8 or higher is required!")
            print(f"   Current version: {sys.version}")
            input("\nPress Enter to exit...")
            return
        
        print(f"✅ Python {sys.version.split()[0]} - OK")
        
        # Check and install requirements
        if not check_and_install_requirements():
            print("\n❌ Failed to install required packages")
            input("\nPress Enter to exit...")
            return
        
        # Setup database
        if not setup_database():
            print("\n❌ Failed to setup database")
            response = input("🤔 Continue anyway? (y/n): ").lower().strip()
            if response not in ['y', 'yes', 'نعم', 'ن']:
                return
        
        # Show help
        show_help()
        
        # Ask user if ready to start
        print("\n🎯 Ready to start the Enhanced ERP System!")
        response = input("Press Enter to continue or 'q' to quit: ").lower().strip()
        
        if response in ['q', 'quit', 'exit', 'خروج']:
            print("👋 Goodbye!")
            return
        
        # Find and run application
        if not find_and_run_app():
            print("\n❌ Failed to start application")
            print("💡 Try running setup_enhanced.py first")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("💡 Try running the setup script: python setup_enhanced.py")
    
    finally:
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
