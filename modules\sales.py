"""
Sales Management Module for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from datetime import datetime, date
from gui.base_window import BaseWindow
from gui.components import FormFrame, DataTable, SearchFrame, show_message, show_confirmation
from utils import get_text, ValidationUtils, NumberUtils, DateUtils
from auth import auth_manager
from database import DatabaseManager

class SalesWindow(ctk.CTkToplevel):
    """Sales management window"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title(get_text('modules.sales'))
        self.geometry("1400x900")
        
        self.db = DatabaseManager()
        self.selected_order = None
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
        self.load_sales_orders()
    
    def setup_widgets(self):
        """Setup sales management widgets"""
        # Main container
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=get_text('modules.sales'),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=20)
        
        # Toolbar
        self.create_toolbar(main_frame)
        
        # Sales orders list
        self.create_sales_list(main_frame)
    
    def create_toolbar(self, parent):
        """Create toolbar with action buttons"""
        toolbar_frame = ctk.CTkFrame(parent)
        toolbar_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        
        # New order button
        self.new_order_button = ctk.CTkButton(
            toolbar_frame,
            text=f"➕ {get_text('common.new_order')}",
            command=self.new_sales_order,
            width=150
        )
        self.new_order_button.pack(side="left", padx=5, pady=10)
        
        # Edit order button
        self.edit_order_button = ctk.CTkButton(
            toolbar_frame,
            text=f"✏️ {get_text('common.edit')}",
            command=self.edit_sales_order,
            width=100,
            state="disabled"
        )
        self.edit_order_button.pack(side="left", padx=5, pady=10)
        
        # View order button
        self.view_order_button = ctk.CTkButton(
            toolbar_frame,
            text=f"👁️ {get_text('common.view')}",
            command=self.view_sales_order,
            width=100,
            state="disabled"
        )
        self.view_order_button.pack(side="left", padx=5, pady=10)
        
        # Delete order button
        self.delete_order_button = ctk.CTkButton(
            toolbar_frame,
            text=f"🗑️ {get_text('common.delete')}",
            command=self.delete_sales_order,
            width=100,
            state="disabled",
            fg_color="red"
        )
        self.delete_order_button.pack(side="left", padx=5, pady=10)
        
        # Print order button
        self.print_order_button = ctk.CTkButton(
            toolbar_frame,
            text=f"🖨️ {get_text('common.print')}",
            command=self.print_sales_order,
            width=100,
            state="disabled"
        )
        self.print_order_button.pack(side="left", padx=5, pady=10)
    
    def create_sales_list(self, parent):
        """Create sales orders list"""
        list_frame = ctk.CTkFrame(parent)
        list_frame.grid(row=2, column=0, sticky="nsew", padx=5, pady=5)
        list_frame.grid_rowconfigure(1, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # Search frame
        self.search_frame = SearchFrame(list_frame, on_search=self.search_sales_orders)
        self.search_frame.grid(row=0, column=0, sticky="ew", padx=20, pady=10)
        
        # Sales orders table
        columns = {
            'id': {'text': 'ID', 'width': 50},
            'so_number': {'text': get_text('common.order_number'), 'width': 120},
            'customer_name': {'text': get_text('common.customer'), 'width': 200},
            'order_date': {'text': get_text('common.order_date'), 'width': 100, 'format': 'date'},
            'delivery_date': {'text': get_text('common.delivery_date'), 'width': 100, 'format': 'date'},
            'total_amount': {'text': get_text('common.total'), 'width': 120, 'format': 'currency'},
            'status': {'text': get_text('common.status'), 'width': 100},
            'created_by': {'text': get_text('common.created_by'), 'width': 120}
        }
        
        self.sales_table = DataTable(list_frame, columns)
        self.sales_table.grid(row=1, column=0, sticky="nsew", padx=20, pady=10)
        
        # Bind table events
        self.sales_table.tree.bind('<ButtonRelease-1>', self.on_sales_order_select)
        self.sales_table.tree.bind('<Double-1>', self.on_sales_order_double_click)
    
    def load_sales_orders(self):
        """Load sales orders from database"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get branch filter
            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE so.branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT so.*, c.name as customer_name, u.full_name as created_by
                FROM sales_orders so
                LEFT JOIN customers c ON so.customer_id = c.id
                LEFT JOIN users u ON so.created_by = u.id
                {branch_filter}
                ORDER BY so.created_at DESC
            ''', params)
            
            orders = [dict(row) for row in cursor.fetchall()]
            self.sales_table.load_data(orders)
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading sales orders: {str(e)}", "error")
    
    def search_sales_orders(self, search_term):
        """Search sales orders"""
        if not search_term:
            self.load_sales_orders()
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get branch filter
            branch_filter = ""
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
            if auth_manager.get_user_branch():
                branch_filter = "AND so.branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT so.*, c.name as customer_name, u.full_name as created_by
                FROM sales_orders so
                LEFT JOIN customers c ON so.customer_id = c.id
                LEFT JOIN users u ON so.created_by = u.id
                WHERE (so.so_number LIKE ? OR c.name LIKE ? OR so.notes LIKE ?)
                {branch_filter}
                ORDER BY so.created_at DESC
            ''', params)
            
            orders = [dict(row) for row in cursor.fetchall()]
            self.sales_table.load_data(orders)
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error searching sales orders: {str(e)}", "error")
    
    def on_sales_order_select(self, event):
        """Handle sales order selection"""
        selected_order = self.sales_table.get_selected_item()
        if selected_order:
            self.selected_order = selected_order
            
            # Enable action buttons
            self.edit_order_button.configure(state="normal")
            self.view_order_button.configure(state="normal")
            self.delete_order_button.configure(state="normal")
            self.print_order_button.configure(state="normal")
    
    def on_sales_order_double_click(self, event):
        """Handle sales order double click"""
        self.view_sales_order()
    
    def new_sales_order(self):
        """Create new sales order"""
        # Check permissions
        if not auth_manager.has_permission('sales', 'create'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        order_window = SalesOrderWindow(self)
        order_window.grab_set()
    
    def edit_sales_order(self):
        """Edit selected sales order"""
        if not self.selected_order:
            return
        
        # Check permissions
        if not auth_manager.has_permission('sales', 'update'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        order_window = SalesOrderWindow(self, self.selected_order['id'])
        order_window.grab_set()
    
    def view_sales_order(self):
        """View selected sales order"""
        if not self.selected_order:
            return
        
        order_window = SalesOrderWindow(self, self.selected_order['id'], view_only=True)
        order_window.grab_set()
    
    def delete_sales_order(self):
        """Delete selected sales order"""
        if not self.selected_order:
            return
        
        # Check permissions
        if not auth_manager.has_permission('sales', 'delete'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Confirm deletion
        if not show_confirmation(
            get_text('common.confirm'),
            f"{get_text('common.confirm_delete')} {self.selected_order['so_number']}?"
        ):
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Delete order items first (cascade should handle this)
            cursor.execute('DELETE FROM sales_order_items WHERE so_id = ?', (self.selected_order['id'],))
            
            # Delete order
            cursor.execute('DELETE FROM sales_orders WHERE id = ?', (self.selected_order['id'],))
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="delete_sales_order",
                table_name="sales_orders",
                record_id=self.selected_order['id']
            )
            
            show_message(get_text('common.success'), get_text('success.order_deleted'), "success")
            self.load_sales_orders()
            
            # Disable action buttons
            self.edit_order_button.configure(state="disabled")
            self.view_order_button.configure(state="disabled")
            self.delete_order_button.configure(state="disabled")
            self.print_order_button.configure(state="disabled")
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error deleting sales order: {str(e)}", "error")
    
    def print_sales_order(self):
        """Print selected sales order"""
        if not self.selected_order:
            return
        
        # TODO: Implement printing functionality
        show_message(get_text('common.info'), get_text('info.feature_coming_soon'), "info")

class SalesOrderWindow(ctk.CTkToplevel):
    """Sales order creation/editing window"""
    
    def __init__(self, parent, order_id=None, view_only=False):
        super().__init__(parent)
        
        self.parent = parent
        self.order_id = order_id
        self.view_only = view_only
        self.db = DatabaseManager()
        self.order_items = []
        
        title = get_text('common.view_order') if view_only else (
            get_text('common.edit_order') if order_id else get_text('common.new_order')
        )
        self.title(title)
        self.geometry("1000x700")
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
        self.load_customers()
        self.load_products()
        
        if order_id:
            self.load_order_data()
        else:
            self.generate_order_number()
    
    def setup_widgets(self):
        """Setup sales order widgets"""
        # Main container
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        main_frame.grid_rowconfigure(2, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)
        
        # Header
        self.create_header(main_frame)
        
        # Order details
        self.create_order_details(main_frame)
        
        # Order items
        self.create_order_items(main_frame)
        
        # Footer with totals and buttons
        self.create_footer(main_frame)
    
    def create_header(self, parent):
        """Create order header"""
        header_frame = ctk.CTkFrame(parent)
        header_frame.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        title = get_text('common.view_order') if self.view_only else (
            get_text('common.edit_order') if self.order_id else get_text('common.new_order')
        )
        
        title_label = ctk.CTkLabel(
            header_frame,
            text=title,
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=15)
    
    def create_order_details(self, parent):
        """Create order details form"""
        details_frame = ctk.CTkFrame(parent)
        details_frame.grid(row=1, column=0, sticky="ew", pady=(0, 10))
        details_frame.grid_columnconfigure(1, weight=1)
        details_frame.grid_columnconfigure(3, weight=1)
        
        # Order number
        order_num_label = ctk.CTkLabel(details_frame, text=get_text('common.order_number'))
        order_num_label.grid(row=0, column=0, sticky="w", padx=10, pady=5)
        
        self.order_number_var = tk.StringVar()
        self.order_number_entry = ctk.CTkEntry(
            details_frame, 
            textvariable=self.order_number_var,
            state="readonly" if self.view_only else "normal"
        )
        self.order_number_entry.grid(row=0, column=1, sticky="ew", padx=10, pady=5)
        
        # Customer
        customer_label = ctk.CTkLabel(details_frame, text=get_text('common.customer'))
        customer_label.grid(row=0, column=2, sticky="w", padx=10, pady=5)
        
        self.customer_combobox = ctk.CTkComboBox(
            details_frame,
            state="readonly" if self.view_only else "normal"
        )
        self.customer_combobox.grid(row=0, column=3, sticky="ew", padx=10, pady=5)
        
        # Order date
        order_date_label = ctk.CTkLabel(details_frame, text=get_text('common.order_date'))
        order_date_label.grid(row=1, column=0, sticky="w", padx=10, pady=5)
        
        self.order_date_var = tk.StringVar()
        self.order_date_var.set(DateUtils.format_date(DateUtils.get_current_date()))
        self.order_date_entry = ctk.CTkEntry(
            details_frame, 
            textvariable=self.order_date_var,
            state="readonly" if self.view_only else "normal"
        )
        self.order_date_entry.grid(row=1, column=1, sticky="ew", padx=10, pady=5)
        
        # Delivery date
        delivery_date_label = ctk.CTkLabel(details_frame, text=get_text('common.delivery_date'))
        delivery_date_label.grid(row=1, column=2, sticky="w", padx=10, pady=5)
        
        self.delivery_date_var = tk.StringVar()
        self.delivery_date_entry = ctk.CTkEntry(
            details_frame, 
            textvariable=self.delivery_date_var,
            state="readonly" if self.view_only else "normal"
        )
        self.delivery_date_entry.grid(row=1, column=3, sticky="ew", padx=10, pady=5)
        
        # Notes
        notes_label = ctk.CTkLabel(details_frame, text=get_text('common.notes'))
        notes_label.grid(row=2, column=0, sticky="w", padx=10, pady=5)
        
        self.notes_text = ctk.CTkTextbox(
            details_frame, 
            height=60,
            state="disabled" if self.view_only else "normal"
        )
        self.notes_text.grid(row=2, column=1, columnspan=3, sticky="ew", padx=10, pady=5)
    
    def create_order_items(self, parent):
        """Create order items section"""
        items_frame = ctk.CTkFrame(parent)
        items_frame.grid(row=2, column=0, sticky="nsew", pady=(0, 10))
        items_frame.grid_rowconfigure(1, weight=1)
        items_frame.grid_columnconfigure(0, weight=1)
        
        # Items header
        items_header = ctk.CTkFrame(items_frame)
        items_header.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        
        items_title = ctk.CTkLabel(
            items_header,
            text=get_text('common.order_items'),
            font=ctk.CTkFont(size=16, weight="bold")
        )
        items_title.pack(side="left", padx=10, pady=10)
        
        if not self.view_only:
            add_item_button = ctk.CTkButton(
                items_header,
                text=f"➕ {get_text('common.add_item')}",
                command=self.add_item,
                width=120
            )
            add_item_button.pack(side="right", padx=10, pady=10)
        
        # Items table
        columns = {
            'product_name': {'text': get_text('common.product'), 'width': 200},
            'quantity': {'text': get_text('common.quantity'), 'width': 100, 'format': 'number'},
            'unit_price': {'text': get_text('common.unit_price'), 'width': 120, 'format': 'currency'},
            'discount_percent': {'text': get_text('common.discount') + ' %', 'width': 100},
            'total_price': {'text': get_text('common.total'), 'width': 120, 'format': 'currency'}
        }
        
        self.items_table = DataTable(items_frame, columns)
        self.items_table.grid(row=1, column=0, sticky="nsew", padx=10, pady=10)
        
        if not self.view_only:
            # Bind double-click to edit item
            self.items_table.tree.bind('<Double-1>', self.edit_item)
            # Bind right-click for context menu
            self.items_table.tree.bind('<Button-3>', self.show_item_context_menu)
    
    def create_footer(self, parent):
        """Create footer with totals and buttons"""
        footer_frame = ctk.CTkFrame(parent)
        footer_frame.grid(row=3, column=0, sticky="ew")
        footer_frame.grid_columnconfigure(0, weight=1)
        
        # Totals frame
        totals_frame = ctk.CTkFrame(footer_frame)
        totals_frame.grid(row=0, column=0, sticky="e", padx=10, pady=10)
        
        # Subtotal
        subtotal_label = ctk.CTkLabel(totals_frame, text=f"{get_text('common.subtotal')}:")
        subtotal_label.grid(row=0, column=0, sticky="e", padx=5, pady=2)
        
        self.subtotal_var = tk.StringVar(value="0.00")
        subtotal_value = ctk.CTkLabel(totals_frame, textvariable=self.subtotal_var)
        subtotal_value.grid(row=0, column=1, sticky="w", padx=5, pady=2)
        
        # Tax
        tax_label = ctk.CTkLabel(totals_frame, text=f"{get_text('common.tax')}:")
        tax_label.grid(row=1, column=0, sticky="e", padx=5, pady=2)
        
        self.tax_var = tk.StringVar(value="0.00")
        tax_value = ctk.CTkLabel(totals_frame, textvariable=self.tax_var)
        tax_value.grid(row=1, column=1, sticky="w", padx=5, pady=2)
        
        # Total
        total_label = ctk.CTkLabel(
            totals_frame, 
            text=f"{get_text('common.total')}:",
            font=ctk.CTkFont(weight="bold")
        )
        total_label.grid(row=2, column=0, sticky="e", padx=5, pady=2)
        
        self.total_var = tk.StringVar(value="0.00")
        total_value = ctk.CTkLabel(
            totals_frame, 
            textvariable=self.total_var,
            font=ctk.CTkFont(weight="bold")
        )
        total_value.grid(row=2, column=1, sticky="w", padx=5, pady=2)
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(footer_frame)
        buttons_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=10)
        
        if not self.view_only:
            # Save button
            save_button = ctk.CTkButton(
                buttons_frame,
                text=get_text('common.save'),
                command=self.save_order,
                width=100
            )
            save_button.pack(side="left", padx=5)
        
        # Close button
        close_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.close'),
            command=self.destroy,
            width=100
        )
        close_button.pack(side="right", padx=5)
    
    def load_customers(self):
        """Load customers for combobox"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT id, name FROM customers 
                {branch_filter}
                AND is_active = 1 
                ORDER BY name
            ''', params)
            
            customers = cursor.fetchall()
            conn.close()
            
            customer_values = [f"{customer['id']} - {customer['name']}" for customer in customers]
            self.customer_combobox.configure(values=customer_values)
            
        except Exception as e:
            print(f"Error loading customers: {e}")
    
    def load_products(self):
        """Load products for item selection"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT id, code, name, selling_price, current_stock 
                FROM products 
                {branch_filter}
                AND is_active = 1 
                ORDER BY name
            ''', params)
            
            self.products = [dict(row) for row in cursor.fetchall()]
            conn.close()
            
        except Exception as e:
            print(f"Error loading products: {e}")
            self.products = []
    
    def generate_order_number(self):
        """Generate new order number"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get next order number
            cursor.execute('''
                SELECT COUNT(*) + 1 as next_num FROM sales_orders 
                WHERE strftime('%Y', order_date) = strftime('%Y', 'now')
            ''')
            
            next_num = cursor.fetchone()['next_num']
            conn.close()
            
            # Format: SO-YYYY-NNNN
            order_number = f"SO-{datetime.now().year}-{next_num:04d}"
            self.order_number_var.set(order_number)
            
        except Exception as e:
            print(f"Error generating order number: {e}")
            self.order_number_var.set("SO-NEW")
    
    def load_order_data(self):
        """Load existing order data"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Load order header
            cursor.execute('''
                SELECT so.*, c.name as customer_name
                FROM sales_orders so
                LEFT JOIN customers c ON so.customer_id = c.id
                WHERE so.id = ?
            ''', (self.order_id,))
            
            order = cursor.fetchone()
            if order:
                order = dict(order)
                
                # Populate form
                self.order_number_var.set(order['so_number'])
                self.customer_combobox.set(f"{order['customer_id']} - {order['customer_name']}")
                self.order_date_var.set(DateUtils.format_date(order['order_date']))
                if order['delivery_date']:
                    self.delivery_date_var.set(DateUtils.format_date(order['delivery_date']))
                if order['notes']:
                    self.notes_text.insert("1.0", order['notes'])
            
            # Load order items
            cursor.execute('''
                SELECT soi.*, p.name as product_name
                FROM sales_order_items soi
                LEFT JOIN products p ON soi.product_id = p.id
                WHERE soi.so_id = ?
            ''', (self.order_id,))
            
            self.order_items = [dict(row) for row in cursor.fetchall()]
            self.refresh_items_table()
            self.calculate_totals()
            
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading order data: {str(e)}", "error")
    
    def add_item(self):
        """Add new item to order"""
        item_window = OrderItemWindow(self, self.products)
        item_window.grab_set()
    
    def edit_item(self, event):
        """Edit selected item"""
        if self.view_only:
            return
        
        selected_item = self.items_table.get_selected_item()
        if selected_item:
            # Find item in order_items list
            for i, item in enumerate(self.order_items):
                if (item.get('product_name') == selected_item['product_name'] and
                    item.get('quantity') == selected_item['quantity']):
                    item_window = OrderItemWindow(self, self.products, item, i)
                    item_window.grab_set()
                    break
    
    def show_item_context_menu(self, event):
        """Show context menu for items"""
        if self.view_only:
            return
        
        # TODO: Implement context menu with delete option
        pass
    
    def add_order_item(self, item_data, index=None):
        """Add or update order item"""
        if index is not None:
            # Update existing item
            self.order_items[index] = item_data
        else:
            # Add new item
            self.order_items.append(item_data)
        
        self.refresh_items_table()
        self.calculate_totals()
    
    def refresh_items_table(self):
        """Refresh items table"""
        self.items_table.load_data(self.order_items)
    
    def calculate_totals(self):
        """Calculate order totals"""
        subtotal = sum(item.get('total_price', 0) for item in self.order_items)
        tax_rate = 0.15  # 15% tax rate - should be configurable
        tax_amount = subtotal * tax_rate
        total = subtotal + tax_amount
        
        self.subtotal_var.set(NumberUtils.format_currency(subtotal))
        self.tax_var.set(NumberUtils.format_currency(tax_amount))
        self.total_var.set(NumberUtils.format_currency(total))
    
    def save_order(self):
        """Save sales order"""
        try:
            # Validate form
            if not self.order_number_var.get():
                show_message(get_text('common.error'), get_text('error.order_number_required'), "error")
                return
            
            if not self.customer_combobox.get():
                show_message(get_text('common.error'), get_text('error.customer_required'), "error")
                return
            
            if not self.order_items:
                show_message(get_text('common.error'), get_text('error.no_items'), "error")
                return
            
            # Extract customer ID
            customer_id = int(self.customer_combobox.get().split(' - ')[0])
            
            # Parse dates
            order_date = DateUtils.parse_date(self.order_date_var.get())
            delivery_date = DateUtils.parse_date(self.delivery_date_var.get()) if self.delivery_date_var.get() else None
            
            # Calculate totals
            subtotal = sum(item.get('total_price', 0) for item in self.order_items)
            tax_amount = subtotal * 0.15  # 15% tax
            total_amount = subtotal + tax_amount
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            if self.order_id:
                # Update existing order
                cursor.execute('''
                    UPDATE sales_orders SET
                        so_number = ?, customer_id = ?, order_date = ?, delivery_date = ?,
                        subtotal = ?, tax_amount = ?, total_amount = ?, notes = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (
                    self.order_number_var.get(), customer_id, order_date, delivery_date,
                    subtotal, tax_amount, total_amount, self.notes_text.get("1.0", "end-1c"),
                    self.order_id
                ))
                
                # Delete existing items
                cursor.execute('DELETE FROM sales_order_items WHERE so_id = ?', (self.order_id,))
                
                order_id = self.order_id
                action = "update_sales_order"
            else:
                # Create new order
                cursor.execute('''
                    INSERT INTO sales_orders (
                        so_number, customer_id, order_date, delivery_date,
                        subtotal, tax_amount, total_amount, notes, created_by, branch_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.order_number_var.get(), customer_id, order_date, delivery_date,
                    subtotal, tax_amount, total_amount, self.notes_text.get("1.0", "end-1c"),
                    auth_manager.get_user_id(),
                    auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None
                ))
                
                order_id = cursor.lastrowid
                action = "create_sales_order"
            
            # Insert order items
            for item in self.order_items:
                cursor.execute('''
                    INSERT INTO sales_order_items (
                        so_id, product_id, quantity, unit_price, discount_percent, total_price
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    order_id, item['product_id'], item['quantity'],
                    item['unit_price'], item.get('discount_percent', 0), item['total_price']
                ))
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action=action,
                table_name="sales_orders",
                record_id=order_id
            )
            
            show_message(get_text('common.success'), get_text('success.order_saved'), "success")
            self.parent.load_sales_orders()
            self.destroy()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error saving order: {str(e)}", "error")

class OrderItemWindow(ctk.CTkToplevel):
    """Order item dialog"""
    
    def __init__(self, parent, products, item_data=None, item_index=None):
        super().__init__(parent)
        
        self.parent = parent
        self.products = products
        self.item_data = item_data
        self.item_index = item_index
        
        self.title(get_text('common.edit_item') if item_data else get_text('common.add_item'))
        self.geometry("400x350")
        self.resizable(False, False)
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
        
        if item_data:
            self.load_item_data()
    
    def setup_widgets(self):
        """Setup item dialog widgets"""
        # Main frame
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Product selection
        product_label = ctk.CTkLabel(main_frame, text=get_text('common.product'))
        product_label.grid(row=0, column=0, sticky="w", padx=5, pady=5)
        
        product_values = [f"{p['id']} - {p['name']}" for p in self.products]
        self.product_combobox = ctk.CTkComboBox(main_frame, values=product_values, width=300)
        self.product_combobox.grid(row=0, column=1, padx=5, pady=5)
        self.product_combobox.bind("<<ComboboxSelected>>", self.on_product_select)
        
        # Quantity
        qty_label = ctk.CTkLabel(main_frame, text=get_text('common.quantity'))
        qty_label.grid(row=1, column=0, sticky="w", padx=5, pady=5)
        
        self.quantity_var = tk.StringVar()
        self.quantity_entry = ctk.CTkEntry(main_frame, textvariable=self.quantity_var, width=300)
        self.quantity_entry.grid(row=1, column=1, padx=5, pady=5)
        self.quantity_entry.bind('<KeyRelease>', self.calculate_total)
        
        # Unit price
        price_label = ctk.CTkLabel(main_frame, text=get_text('common.unit_price'))
        price_label.grid(row=2, column=0, sticky="w", padx=5, pady=5)
        
        self.unit_price_var = tk.StringVar()
        self.unit_price_entry = ctk.CTkEntry(main_frame, textvariable=self.unit_price_var, width=300)
        self.unit_price_entry.grid(row=2, column=1, padx=5, pady=5)
        self.unit_price_entry.bind('<KeyRelease>', self.calculate_total)
        
        # Discount
        discount_label = ctk.CTkLabel(main_frame, text=get_text('common.discount') + ' %')
        discount_label.grid(row=3, column=0, sticky="w", padx=5, pady=5)
        
        self.discount_var = tk.StringVar(value="0")
        self.discount_entry = ctk.CTkEntry(main_frame, textvariable=self.discount_var, width=300)
        self.discount_entry.grid(row=3, column=1, padx=5, pady=5)
        self.discount_entry.bind('<KeyRelease>', self.calculate_total)
        
        # Total
        total_label = ctk.CTkLabel(main_frame, text=get_text('common.total'))
        total_label.grid(row=4, column=0, sticky="w", padx=5, pady=5)
        
        self.total_var = tk.StringVar(value="0.00")
        total_value = ctk.CTkLabel(main_frame, textvariable=self.total_var, width=300)
        total_value.grid(row=4, column=1, padx=5, pady=5)
        
        # Buttons
        buttons_frame = ctk.CTkFrame(main_frame)
        buttons_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        save_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.save'),
            command=self.save_item
        )
        save_button.pack(side="left", padx=10)
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.cancel'),
            command=self.destroy
        )
        cancel_button.pack(side="right", padx=10)
    
    def on_product_select(self, event):
        """Handle product selection"""
        selected = self.product_combobox.get()
        if selected:
            product_id = int(selected.split(' - ')[0])
            product = next((p for p in self.products if p['id'] == product_id), None)
            if product:
                self.unit_price_var.set(str(product['selling_price']))
                self.calculate_total()
    
    def calculate_total(self, event=None):
        """Calculate item total"""
        try:
            quantity = float(self.quantity_var.get() or 0)
            unit_price = float(self.unit_price_var.get() or 0)
            discount_percent = float(self.discount_var.get() or 0)
            
            subtotal = quantity * unit_price
            discount_amount = subtotal * (discount_percent / 100)
            total = subtotal - discount_amount
            
            self.total_var.set(f"{total:.2f}")
            
        except ValueError:
            self.total_var.set("0.00")
    
    def load_item_data(self):
        """Load existing item data"""
        if self.item_data:
            # Set product
            for product in self.products:
                if product['id'] == self.item_data.get('product_id'):
                    self.product_combobox.set(f"{product['id']} - {product['name']}")
                    break
            
            self.quantity_var.set(str(self.item_data.get('quantity', 0)))
            self.unit_price_var.set(str(self.item_data.get('unit_price', 0)))
            self.discount_var.set(str(self.item_data.get('discount_percent', 0)))
            self.calculate_total()
    
    def save_item(self):
        """Save item data"""
        try:
            # Validate
            if not self.product_combobox.get():
                show_message(get_text('common.error'), get_text('error.product_required'), "error")
                return
            
            quantity = float(self.quantity_var.get() or 0)
            if quantity <= 0:
                show_message(get_text('common.error'), get_text('error.invalid_quantity'), "error")
                return
            
            unit_price = float(self.unit_price_var.get() or 0)
            if unit_price <= 0:
                show_message(get_text('common.error'), get_text('error.invalid_price'), "error")
                return
            
            # Get product info
            product_id = int(self.product_combobox.get().split(' - ')[0])
            product = next((p for p in self.products if p['id'] == product_id), None)
            
            # Create item data
            item_data = {
                'product_id': product_id,
                'product_name': product['name'],
                'quantity': quantity,
                'unit_price': unit_price,
                'discount_percent': float(self.discount_var.get() or 0),
                'total_price': float(self.total_var.get())
            }
            
            # Add to parent
            self.parent.add_order_item(item_data, self.item_index)
            self.destroy()
            
        except ValueError:
            show_message(get_text('common.error'), get_text('error.invalid_number'), "error")
        except Exception as e:
            show_message(get_text('common.error'), f"Error saving item: {str(e)}", "error")

if __name__ == "__main__":
    # Test sales module
    app = ctk.CTk()
    sales_window = SalesWindow(app)
    app.mainloop()
