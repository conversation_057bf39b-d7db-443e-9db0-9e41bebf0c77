# Developer Guide - ERP Application

## 🏗️ Architecture Overview

The ERP application follows a modular architecture with clear separation of concerns:

```
├── main.py                 # Application entry point
├── config.py              # Configuration management
├── database.py            # Database layer
├── auth.py                # Authentication system
├── utils.py               # Utility functions
├── gui/                   # GUI framework
│   ├── base_window.py     # Base window class
│   ├── components.py      # Reusable components
│   ├── login_window.py    # Login interface
│   └── dashboard.py       # Main dashboard
├── modules/               # Business modules
│   ├── customers.py       # Customer management
│   ├── suppliers.py       # Supplier management
│   ├── inventory.py       # Inventory management
│   └── sales.py          # Sales management
└── languages/            # Internationalization
    ├── ar.json           # Arabic translations
    └── en.json           # English translations
```

## 🔧 Adding New Modules

### Step 1: Create Module File

Create a new file in the `modules/` directory following this template:

```python
"""
New Module for the ERP application
"""
import customtkinter as ctk
from gui.base_window import BaseWindow
from gui.components import FormFrame, DataTable, SearchFrame, show_message
from utils import get_text, ValidationUtils
from auth import auth_manager
from database import DatabaseManager

class NewModuleWindow(ctk.CTkToplevel):
    """New module window"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title(get_text('modules.new_module'))
        self.geometry("1200x800")
        
        self.db = DatabaseManager()
        self.selected_item = None
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
        self.load_data()
    
    def setup_widgets(self):
        """Setup module widgets"""
        # Implementation here
        pass
    
    def load_data(self):
        """Load data from database"""
        # Implementation here
        pass
```

### Step 2: Add Database Tables

Add new tables in `database.py` within the `init_database()` method:

```python
# New Module Table
cursor.execute('''
    CREATE TABLE IF NOT EXISTS new_module_table (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT 1,
        branch_id INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (branch_id) REFERENCES branches (id)
    )
''')
```

### Step 3: Add Translations

Add translations in both `languages/ar.json` and `languages/en.json`:

```json
{
  "modules": {
    "new_module": "الوحدة الجديدة"
  }
}
```

### Step 4: Add to Dashboard

Add the module to the dashboard in `gui/dashboard.py`:

```python
def create_navigation_menu(self):
    modules = [
        # ... existing modules
        ("🆕", get_text('modules.new_module'), "وصف الوحدة", self.open_new_module),
    ]

def open_new_module(self):
    """Open new module"""
    try:
        from modules.new_module import NewModuleWindow
        new_module_window = NewModuleWindow(self)
        new_module_window.grab_set()
    except ImportError:
        show_message(get_text('common.info'), get_text('info.module_coming_soon'), "info")
```

### Step 5: Add Permissions

Add permissions in the default roles in `database.py`:

```python
admin_permissions = {
    # ... existing permissions
    "new_module": ["create", "read", "update", "delete"],
}
```

## 🎨 GUI Components

### FormFrame Usage

```python
# Create form
self.form = FormFrame(parent_frame)

# Add fields
self.form.add_field('name', 'Name', required=True)
self.form.add_field('email', 'Email', validator=ValidationUtils.validate_email)
self.form.add_field('date', 'Date', field_type="date")
self.form.add_field('amount', 'Amount', field_type="number")

# Get/Set values
data = self.form.get_form_data()
self.form.set_value('name', 'John Doe')

# Validate
errors = self.form.validate_form()
```

### DataTable Usage

```python
# Define columns
columns = {
    'id': {'text': 'ID', 'width': 50},
    'name': {'text': 'Name', 'width': 200},
    'amount': {'text': 'Amount', 'width': 100, 'format': 'currency'},
    'date': {'text': 'Date', 'width': 100, 'format': 'date'}
}

# Create table
self.table = DataTable(parent_frame, columns)

# Load data
self.table.load_data(data_list)

# Get selected item
selected = self.table.get_selected_item()
```

## 🔐 Authentication & Permissions

### Check Permissions

```python
# Check if user has permission
if not auth_manager.has_permission('module_name', 'action'):
    show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
    return

# Get current user info
user_id = auth_manager.get_user_id()
user_name = auth_manager.get_user_name()
branch = auth_manager.get_user_branch()
```

### Log Activities

```python
# Log user activity
self.db.log_activity(
    user_id=auth_manager.get_user_id(),
    action="create_record",
    table_name="table_name",
    record_id=record_id,
    new_values=data
)
```

## 🌐 Internationalization

### Adding New Text

1. Add to `languages/ar.json`:
```json
{
  "new_section": {
    "new_text": "النص الجديد"
  }
}
```

2. Add to `languages/en.json`:
```json
{
  "new_section": {
    "new_text": "New Text"
  }
}
```

3. Use in code:
```python
text = get_text('new_section.new_text')
```

### RTL Support

```python
# Check if current language is RTL
if is_rtl():
    # Apply RTL-specific layout
    widget.configure(justify='right')

# Format Arabic text
formatted_text = format_arabic_text(arabic_text)
```

## 🗄️ Database Operations

### Basic CRUD Operations

```python
def create_record(self, data):
    """Create new record"""
    conn = self.db.get_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO table_name (field1, field2, created_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
    ''', (data['field1'], data['field2']))
    
    record_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    return record_id

def read_records(self, filters=None):
    """Read records with optional filters"""
    conn = self.db.get_connection()
    cursor = conn.cursor()
    
    query = "SELECT * FROM table_name WHERE 1=1"
    params = []
    
    if filters:
        for key, value in filters.items():
            query += f" AND {key} = ?"
            params.append(value)
    
    cursor.execute(query, params)
    records = [dict(row) for row in cursor.fetchall()]
    conn.close()
    
    return records

def update_record(self, record_id, data):
    """Update existing record"""
    conn = self.db.get_connection()
    cursor = conn.cursor()
    
    # Get old values for audit
    cursor.execute('SELECT * FROM table_name WHERE id = ?', (record_id,))
    old_values = dict(cursor.fetchone())
    
    # Update record
    cursor.execute('''
        UPDATE table_name SET field1 = ?, field2 = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ''', (data['field1'], data['field2'], record_id))
    
    conn.commit()
    conn.close()
    
    return old_values

def delete_record(self, record_id):
    """Delete record (soft delete recommended)"""
    conn = self.db.get_connection()
    cursor = conn.cursor()
    
    # Soft delete
    cursor.execute('''
        UPDATE table_name SET is_active = 0, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ''', (record_id,))
    
    conn.commit()
    conn.close()
```

## 🧪 Testing

### Manual Testing Checklist

For each new module:

1. **Functionality Testing**
   - [ ] Create new records
   - [ ] Read/display records
   - [ ] Update existing records
   - [ ] Delete records
   - [ ] Search and filter

2. **Permission Testing**
   - [ ] Admin user can access all functions
   - [ ] Regular user respects permissions
   - [ ] Unauthorized actions show error

3. **UI Testing**
   - [ ] Forms validate correctly
   - [ ] Tables display data properly
   - [ ] Arabic text displays correctly
   - [ ] English text displays correctly

4. **Database Testing**
   - [ ] Data saves correctly
   - [ ] Foreign keys work
   - [ ] Audit log records activities

## 🔧 Debugging

### Common Issues

1. **Import Errors**
   ```python
   # Use try-catch for module imports
   try:
       from modules.new_module import NewModuleWindow
   except ImportError:
       show_message("Info", "Module under development", "info")
   ```

2. **Database Errors**
   ```python
   # Always use try-catch for database operations
   try:
       # Database operation
       pass
   except Exception as e:
       show_message("Error", f"Database error: {str(e)}", "error")
   ```

3. **GUI Errors**
   ```python
   # Check if widgets exist before accessing
   if hasattr(self, 'widget_name'):
       self.widget_name.configure(text="New Text")
   ```

### Logging

```python
# Add logging for debugging
import logging

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

logger.debug("Debug message")
logger.info("Info message")
logger.error("Error message")
```

## 📦 Building and Distribution

### Creating Executable

```bash
# Install PyInstaller
pip install pyinstaller

# Create executable
pyinstaller --onefile --windowed main.py

# With icon
pyinstaller --onefile --windowed --icon=icon.ico main.py
```

### Package Structure

```
dist/
├── main.exe              # Executable
├── languages/            # Language files
├── requirements.txt      # Dependencies
└── README.md            # Documentation
```

## 🚀 Performance Optimization

### Database Optimization

1. **Use Indexes**
   ```sql
   CREATE INDEX idx_table_field ON table_name(field_name);
   ```

2. **Limit Query Results**
   ```python
   cursor.execute("SELECT * FROM table_name LIMIT 1000")
   ```

3. **Use Prepared Statements**
   ```python
   cursor.execute("SELECT * FROM table_name WHERE field = ?", (value,))
   ```

### GUI Optimization

1. **Lazy Loading**
   ```python
   # Load data only when needed
   def on_tab_select(self):
       if not self.data_loaded:
           self.load_data()
           self.data_loaded = True
   ```

2. **Virtual Scrolling**
   ```python
   # For large datasets, implement virtual scrolling
   # Load only visible items
   ```

## 📚 Best Practices

### Code Style

1. **Follow PEP 8**
2. **Use meaningful variable names**
3. **Add docstrings to functions**
4. **Handle exceptions properly**
5. **Use type hints where appropriate**

### Security

1. **Validate all user inputs**
2. **Use parameterized queries**
3. **Log security events**
4. **Implement proper authentication**

### Maintainability

1. **Keep functions small and focused**
2. **Use consistent naming conventions**
3. **Document complex logic**
4. **Write reusable components**

---

This guide provides the foundation for extending and maintaining the ERP application. For specific implementation details, refer to the existing modules as examples.
