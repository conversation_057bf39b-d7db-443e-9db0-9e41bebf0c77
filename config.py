"""
Configuration settings for the ERP application
"""
import os
import json
from pathlib import Path

class Config:
    # Application settings
    APP_NAME = "نظام إدارة الأعمال المتكامل - Integrated Business Management System"
    VERSION = "1.0.0"
    
    # Database settings
    DATABASE_PATH = "erp_database.db"
    BACKUP_DIR = "backups"
    
    # GUI settings
    WINDOW_WIDTH = 1200
    WINDOW_HEIGHT = 800
    MIN_WIDTH = 800
    MIN_HEIGHT = 600
    
    # Language settings
    DEFAULT_LANGUAGE = "ar"  # Arabic by default
    SUPPORTED_LANGUAGES = ["ar", "en"]
    LANGUAGE_DIR = "languages"
    
    # Security settings
    SESSION_TIMEOUT = 3600  # 1 hour in seconds
    PASSWORD_MIN_LENGTH = 8
    MAX_LOGIN_ATTEMPTS = 3
    
    # Currency and formatting
    DEFAULT_CURRENCY = "ريال سعودي"
    CURRENCY_SYMBOL = "ر.س"
    DATE_FORMAT = "%Y-%m-%d"
    DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # Notification settings
    NOTIFICATION_DURATION = 5000  # 5 seconds
    
    # Report settings
    REPORTS_DIR = "reports"
    TEMP_DIR = "temp"
    
    # Backup settings
    AUTO_BACKUP_ENABLED = True
    BACKUP_INTERVAL_HOURS = 24
    MAX_BACKUP_FILES = 30
    
    @classmethod
    def ensure_directories(cls):
        """Create necessary directories if they don't exist"""
        directories = [
            cls.BACKUP_DIR,
            cls.LANGUAGE_DIR,
            cls.REPORTS_DIR,
            cls.TEMP_DIR
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    @classmethod
    def load_user_settings(cls, user_id=None):
        """Load user-specific settings"""
        settings_file = f"user_settings_{user_id}.json" if user_id else "default_settings.json"
        
        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        
        return {
            "language": cls.DEFAULT_LANGUAGE,
            "theme": "light",
            "currency": cls.DEFAULT_CURRENCY,
            "date_format": cls.DATE_FORMAT
        }
    
    @classmethod
    def save_user_settings(cls, settings, user_id=None):
        """Save user-specific settings"""
        settings_file = f"user_settings_{user_id}.json" if user_id else "default_settings.json"
        
        try:
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
