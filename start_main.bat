@echo off
chcp 65001 >nul
title Enhanced ERP System - Main Application

echo.
echo ================================================================
echo 🏢 Enhanced ERP System - نظام إدارة الأعمال المحسن
echo ================================================================
echo 📱 Main Application - التطبيق الرئيسي المحدث
echo.

echo 🔍 Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
    echo 💡 Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
python --version

echo.
echo 📦 Checking required packages...
python -c "import customtkinter, PIL, matplotlib, pandas, numpy" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Some packages missing. Installing...
    echo 🔧 Installing customtkinter...
    pip install customtkinter >nul 2>&1
    echo 🔧 Installing pillow...
    pip install pillow >nul 2>&1
    echo 🔧 Installing matplotlib...
    pip install matplotlib >nul 2>&1
    echo 🔧 Installing pandas...
    pip install pandas >nul 2>&1
    echo 🔧 Installing numpy...
    pip install numpy >nul 2>&1
    echo ✅ Installation completed!
) else (
    echo ✅ All packages ready
)

echo.
echo 🔐 Login Information:
echo    Username: admin
echo    Password: admin123
echo.

echo 🚀 Starting Enhanced ERP Application...
echo 🌟 Features: Enhanced Login + Smart Dashboard Selection
echo.

python main.py

echo.
echo 👋 Application closed
pause
