"""
Main entry point for the ERP application
"""
import sys
import os
import traceback
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    import customtkinter as ctk
    from tkinter import messagebox
    from config import Config
    from database import DatabaseManager
    from gui.login_window import Lo<PERSON><PERSON>indow
    from utils import get_text, set_language
    
    class ERPApplication:
        """Main ERP Application class"""
        
        def __init__(self):
            self.setup_application()
        
        def setup_application(self):
            """Setup application environment"""
            try:
                # Ensure required directories exist
                Config.ensure_directories()
                
                # Initialize database
                self.init_database()
                
                # Set appearance
                ctk.set_appearance_mode("light")
                ctk.set_default_color_theme("blue")
                
                # Load user settings
                self.load_settings()
                
            except Exception as e:
                self.show_startup_error(f"Application setup failed: {str(e)}")
                sys.exit(1)
        
        def init_database(self):
            """Initialize database"""
            try:
                db = DatabaseManager()
                print("Database initialized successfully")
            except Exception as e:
                raise Exception(f"Database initialization failed: {str(e)}")
        
        def load_settings(self):
            """Load application settings"""
            try:
                settings = Config.load_user_settings()
                
                # Set language
                language = settings.get('language', Config.DEFAULT_LANGUAGE)
                set_language(language)
                
                print(f"Settings loaded - Language: {language}")
                
            except Exception as e:
                print(f"Warning: Could not load settings: {str(e)}")
        
        def show_startup_error(self, message):
            """Show startup error message"""
            try:
                root = ctk.CTk()
                root.withdraw()  # Hide main window
                messagebox.showerror("Startup Error", message)
                root.destroy()
            except:
                print(f"FATAL ERROR: {message}")
        
        def run(self):
            """Run the application"""
            try:
                # Show login window
                login_window = LoginWindow()
                login_window.mainloop()
                
            except Exception as e:
                error_msg = f"Application error: {str(e)}\n\nTraceback:\n{traceback.format_exc()}"
                self.show_startup_error(error_msg)
                sys.exit(1)
    
    def main():
        """Main function"""
        try:
            # Create and run application
            app = ERPApplication()
            app.run()
            
        except KeyboardInterrupt:
            print("\nApplication interrupted by user")
            sys.exit(0)
        except Exception as e:
            print(f"FATAL ERROR: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            sys.exit(1)
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"Import Error: {str(e)}")
    print("\nPlease install required dependencies:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"Unexpected error: {str(e)}")
    print(f"Traceback: {traceback.format_exc()}")
    sys.exit(1)
