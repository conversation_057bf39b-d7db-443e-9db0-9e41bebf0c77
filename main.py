"""
Enhanced Main Entry Point for the ERP Application
نقطة الدخول المحسنة لتطبيق إدارة الأعمال
"""
import sys
import os
import traceback
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Print startup banner
print("🏢" + "=" * 68 + "🏢")
print("🌟  Enhanced ERP System - نظام إدارة الأعمال المحسن  🌟")
print("🏢" + "=" * 68 + "🏢")
print()

try:
    import customtkinter as ctk
    from tkinter import messagebox
    from config import Config
    from database import DatabaseManager
    from gui.login_window import LoginWindow
    from utils import get_text, set_language

    class ERPApplication:
        """Enhanced Main ERP Application class"""

        def __init__(self):
            print("🚀 Initializing Enhanced ERP Application...")
            self.setup_application()

        def setup_application(self):
            """Setup enhanced application environment"""
            try:
                print("📁 Setting up directories...")
                # Ensure required directories exist
                Config.ensure_directories()

                print("🗺️ Initializing database...")
                # Initialize database
                self.init_database()

                print("🎨 Setting up appearance...")
                # Set modern appearance
                ctk.set_appearance_mode("light")
                ctk.set_default_color_theme("blue")

                print("⚙️ Loading settings...")
                # Load user settings
                self.load_settings()

                print("✅ Application setup completed successfully!")

            except Exception as e:
                print(f"❌ Application setup failed: {str(e)}")
                self.show_startup_error(f"Application setup failed: {str(e)}")
                sys.exit(1)

        def init_database(self):
            """Initialize database with enhanced setup"""
            try:
                db = DatabaseManager()
                db.init_database()  # Ensure database is properly initialized
                print("✅ Database initialized successfully")
                return db
            except Exception as e:
                print(f"❌ Database initialization failed: {str(e)}")
                raise Exception(f"Database initialization failed: {str(e)}")

        def load_settings(self):
            """Load enhanced application settings"""
            try:
                settings = Config.load_user_settings()

                # Set language
                language = settings.get('language', Config.DEFAULT_LANGUAGE)
                set_language(language)

                print(f"✅ Settings loaded - Language: {language}")

                # Load theme settings if available
                try:
                    from gui.theme_manager import theme_manager
                    theme_manager.load_saved_theme()
                    print("✅ Theme settings loaded")
                except Exception as theme_error:
                    print(f"⚠️ Theme loading failed: {theme_error}")

            except Exception as e:
                print(f"⚠️ Warning: Could not load settings: {str(e)}")
                # Use default settings
                set_language('ar')  # Default to Arabic

        def show_startup_error(self, message):
            """Show startup error message"""
            try:
                root = ctk.CTk()
                root.withdraw()  # Hide main window
                messagebox.showerror("Startup Error", message)
                root.destroy()
            except:
                print(f"FATAL ERROR: {message}")

        def run(self):
            """Run the enhanced application"""
            try:
                print("🔐 Starting login process...")
                print("🔐 Login Information:")
                print("   Username: admin")
                print("   Password: admin123")
                print()

                # Try enhanced login window first
                try:
                    print("🌟 Attempting to load enhanced login window...")
                    login_window = LoginWindow()

                    # Override the open_main_application method to try enhanced dashboard
                    original_open_main = login_window.open_main_application

                    def open_enhanced_main():
                        """Open enhanced main application"""
                        try:
                            print("🌟 Loading Enhanced Dashboard...")
                            from gui.enhanced_dashboard import EnhancedDashboard
                            dashboard = EnhancedDashboard()
                            dashboard.mainloop()
                        except Exception as e:
                            print(f"⚠️ Enhanced dashboard failed: {e}")
                            print("🔄 Falling back to standard dashboard...")
                            # Fallback to standard dashboard
                            original_open_main()

                    login_window.open_main_application = open_enhanced_main

                    print("✅ Enhanced login window loaded successfully!")
                    login_window.mainloop()

                except Exception as login_error:
                    print(f"⚠️ Enhanced login failed: {login_error}")
                    print("🔄 Trying fallback options...")

                    # Try simple login as fallback
                    try:
                        print("🔄 Loading simple login window...")
                        from simple_login import SimpleLoginWindow
                        simple_login = SimpleLoginWindow()
                        simple_login.mainloop()
                    except Exception as simple_error:
                        print(f"❌ Simple login also failed: {simple_error}")
                        raise login_error  # Re-raise original error

            except Exception as e:
                error_msg = f"Application error: {str(e)}\n\nTraceback:\n{traceback.format_exc()}"
                print(f"❌ {error_msg}")
                self.show_startup_error(error_msg)
                sys.exit(1)

    def main():
        """Enhanced main function with better error handling"""
        try:
            print("🚀 Starting Enhanced ERP Application...")

            # Check Python version
            if sys.version_info < (3, 8):
                print("❌ Python 3.8 or higher is required!")
                print(f"   Current version: {sys.version}")
                input("\nPress Enter to exit...")
                return

            print(f"✅ Python {sys.version.split()[0]} - OK")

            # Check required packages
            required_packages = ['customtkinter', 'PIL', 'matplotlib', 'pandas', 'numpy']
            missing_packages = []

            for package in required_packages:
                try:
                    __import__(package)
                    print(f"✅ {package} - OK")
                except ImportError:
                    print(f"❌ {package} - MISSING")
                    missing_packages.append(package)

            if missing_packages:
                print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
                print("💡 Install with: pip install customtkinter pillow matplotlib pandas numpy")
                response = input("\nContinue anyway? (y/n): ").lower().strip()
                if response not in ['y', 'yes', 'نعم']:
                    return

            # Create and run application
            app = ERPApplication()
            app.run()

        except KeyboardInterrupt:
            print("\n\n⚠️ Application interrupted by user")
            sys.exit(0)
        except Exception as e:
            print(f"\n❌ Fatal error: {str(e)}")
            traceback.print_exc()
            input("\nPress Enter to exit...")
            sys.exit(1)

    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"Import Error: {str(e)}")
    print("\nPlease install required dependencies:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"Unexpected error: {str(e)}")
    print(f"Traceback: {traceback.format_exc()}")
    sys.exit(1)
