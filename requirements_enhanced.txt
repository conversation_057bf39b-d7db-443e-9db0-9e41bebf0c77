# Enhanced ERP System Requirements
# نظام إدارة الأعمال المحسن - المتطلبات

# Core GUI Framework
customtkinter>=5.2.0          # Modern UI framework
tkinter                        # Built-in Python GUI (usually included)

# Image Processing
Pillow>=9.0.0                 # Image processing and icons
pillow-heif>=0.10.0           # HEIF image support (optional)

# Data Visualization
matplotlib>=3.5.0             # Charts and graphs
seaborn>=0.11.0              # Statistical data visualization (optional)

# Data Processing
pandas>=1.4.0                # Data manipulation and analysis
numpy>=1.21.0                # Numerical computing
openpyxl>=3.0.0              # Excel file support

# Database
sqlite3                       # Built-in SQLite support (usually included)
sqlalchemy>=1.4.0            # Advanced database ORM (optional)

# Date and Time
python-dateutil>=2.8.0       # Extended date/time handling

# Internationalization
babel>=2.9.0                 # Internationalization utilities (optional)

# System Integration
psutil>=5.8.0                # System and process utilities
pathlib                      # Path handling (built-in Python 3.4+)

# Development and Testing (optional)
pytest>=6.0.0                # Testing framework
pytest-cov>=3.0.0           # Coverage testing
black>=22.0.0                # Code formatting
flake8>=4.0.0                # Code linting

# Documentation (optional)
sphinx>=4.0.0                # Documentation generation
sphinx-rtd-theme>=1.0.0      # ReadTheDocs theme

# Packaging (optional)
pyinstaller>=5.0.0           # Create standalone executables
auto-py-to-exe>=2.20.0       # GUI for PyInstaller

# Additional Utilities
requests>=2.25.0             # HTTP requests (for future web integration)
cryptography>=3.4.0          # Encryption and security
python-dotenv>=0.19.0        # Environment variables management

# Performance Monitoring (optional)
memory-profiler>=0.60.0      # Memory usage profiling
line-profiler>=3.5.0         # Line-by-line profiling

# Backup and Export (optional)
zipfile                       # Archive creation (built-in)
json                         # JSON handling (built-in)
csv                          # CSV handling (built-in)

# Minimum Python Version
# python>=3.8.0

# Platform-specific requirements
# Windows
# pywin32>=227                # Windows-specific APIs (Windows only)

# macOS
# pyobjc>=8.0                 # macOS-specific APIs (macOS only)

# Linux
# python3-tk                  # Tkinter for Linux (system package)

# Optional Enhanced Features
# reportlab>=3.6.0           # PDF generation
# qrcode>=7.3.0              # QR code generation
# barcode>=1.0.0             # Barcode generation
# fpdf2>=2.5.0               # Simple PDF creation

# Web Integration (future)
# flask>=2.0.0               # Web framework
# flask-cors>=3.0.0          # CORS support
# websockets>=10.0           # WebSocket support

# Mobile Integration (future)
# kivy>=2.1.0                # Cross-platform mobile framework

# Cloud Integration (future)
# boto3>=1.20.0              # AWS SDK
# google-cloud-storage>=2.0.0 # Google Cloud Storage
# azure-storage-blob>=12.0.0  # Azure Blob Storage

# AI/ML Integration (future)
# scikit-learn>=1.0.0        # Machine learning
# tensorflow>=2.8.0          # Deep learning (optional)
# opencv-python>=4.5.0       # Computer vision (optional)

# Installation Notes:
# 1. Install core requirements: pip install -r requirements_enhanced.txt
# 2. For development: pip install -r requirements_enhanced.txt[dev]
# 3. For full features: pip install -r requirements_enhanced.txt[full]

# Quick Installation Commands:
# pip install customtkinter pillow matplotlib pandas numpy openpyxl python-dateutil psutil
# pip install pytest black flake8  # For development
# pip install pyinstaller  # For creating executables
