"""
Supplier Management Module for the ERP application
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
from gui.base_window import BaseWindow
from gui.components import FormFrame, DataTable, SearchFrame, show_message, show_confirmation
from utils import get_text, ValidationUtils, NumberUtils
from auth import auth_manager
from database import DatabaseManager

class SuppliersWindow(ctk.CTkToplevel):
    """Supplier management window"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        self.title(get_text('modules.suppliers'))
        self.geometry("1200x800")
        
        self.db = DatabaseManager()
        self.selected_supplier = None
        
        # Center on parent
        self.transient(parent)
        
        self.setup_widgets()
        self.load_suppliers()
    
    def setup_widgets(self):
        """Setup supplier management widgets"""
        # Main container
        main_frame = ctk.CTkFrame(self)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(1, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text=get_text('modules.suppliers'),
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, pady=20)
        
        # Left panel - Supplier form
        self.create_supplier_form(main_frame)
        
        # Right panel - Supplier list
        self.create_supplier_list(main_frame)
    
    def create_supplier_form(self, parent):
        """Create supplier form"""
        form_frame = ctk.CTkFrame(parent)
        form_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 5), pady=5)
        form_frame.grid_rowconfigure(1, weight=1)
        
        # Form title
        form_title = ctk.CTkLabel(
            form_frame,
            text=get_text('common.supplier_details'),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        form_title.grid(row=0, column=0, pady=15)
        
        # Supplier form
        self.supplier_form = FormFrame(form_frame)
        self.supplier_form.grid(row=1, column=0, sticky="nsew", padx=20, pady=10)
        
        # Add form fields
        self.supplier_form.add_field(
            'name', get_text('common.name'), 
            required=True, width=250
        )
        
        self.supplier_form.add_field(
            'contact_person', get_text('common.contact_person'), 
            width=250
        )
        
        self.supplier_form.add_field(
            'email', get_text('common.email'), 
            validator=ValidationUtils.validate_email,
            width=250
        )
        
        self.supplier_form.add_field(
            'phone', get_text('common.phone'), 
            validator=ValidationUtils.validate_phone,
            width=250
        )
        
        self.supplier_form.add_field(
            'address', get_text('common.address'), 
            field_type="text", width=250
        )
        
        self.supplier_form.add_field(
            'tax_number', get_text('common.tax_number'), 
            validator=ValidationUtils.validate_tax_number,
            width=250
        )
        
        self.supplier_form.add_field(
            'payment_terms', get_text('common.payment_terms'), 
            field_type="number", width=250
        )
        
        self.supplier_form.add_field(
            'credit_limit', get_text('common.credit_limit'), 
            field_type="number", width=250
        )
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(form_frame)
        buttons_frame.grid(row=2, column=0, sticky="ew", padx=20, pady=20)
        
        # Action buttons
        self.save_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.save'),
            command=self.save_supplier,
            width=100
        )
        self.save_button.pack(side="left", padx=5)
        
        self.update_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.update'),
            command=self.update_supplier,
            width=100,
            state="disabled"
        )
        self.update_button.pack(side="left", padx=5)
        
        self.delete_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.delete'),
            command=self.delete_supplier,
            width=100,
            state="disabled",
            fg_color="red"
        )
        self.delete_button.pack(side="left", padx=5)
        
        self.clear_button = ctk.CTkButton(
            buttons_frame,
            text=get_text('common.clear'),
            command=self.clear_form,
            width=100
        )
        self.clear_button.pack(side="left", padx=5)
    
    def create_supplier_list(self, parent):
        """Create supplier list"""
        list_frame = ctk.CTkFrame(parent)
        list_frame.grid(row=1, column=1, sticky="nsew", padx=(5, 0), pady=5)
        list_frame.grid_rowconfigure(2, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)
        
        # List title
        list_title = ctk.CTkLabel(
            list_frame,
            text=get_text('common.supplier_list'),
            font=ctk.CTkFont(size=18, weight="bold")
        )
        list_title.grid(row=0, column=0, pady=15)
        
        # Search frame
        self.search_frame = SearchFrame(list_frame, on_search=self.search_suppliers)
        self.search_frame.grid(row=1, column=0, sticky="ew", padx=20, pady=10)
        
        # Supplier table
        columns = {
            'id': {'text': 'ID', 'width': 50},
            'name': {'text': get_text('common.name'), 'width': 200},
            'contact_person': {'text': get_text('common.contact_person'), 'width': 150},
            'email': {'text': get_text('common.email'), 'width': 180},
            'phone': {'text': get_text('common.phone'), 'width': 120},
            'current_balance': {'text': get_text('common.balance'), 'width': 100, 'format': 'currency'},
            'is_active': {'text': get_text('common.status'), 'width': 80}
        }
        
        self.supplier_table = DataTable(list_frame, columns)
        self.supplier_table.grid(row=2, column=0, sticky="nsew", padx=20, pady=10)
        
        # Bind table events
        self.supplier_table.tree.bind('<ButtonRelease-1>', self.on_supplier_select)
        self.supplier_table.tree.bind('<Double-1>', self.on_supplier_double_click)
    
    def load_suppliers(self):
        """Load suppliers from database"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get branch filter
            branch_filter = ""
            params = []
            if auth_manager.get_user_branch():
                branch_filter = "WHERE branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT id, name, contact_person, email, phone, 
                       current_balance, is_active, created_at
                FROM suppliers 
                {branch_filter}
                ORDER BY name
            ''', params)
            
            suppliers = []
            for row in cursor.fetchall():
                supplier = dict(row)
                supplier['status'] = get_text('common.active') if supplier['is_active'] else get_text('common.inactive')
                suppliers.append(supplier)
            
            self.supplier_table.load_data(suppliers)
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading suppliers: {str(e)}", "error")
    
    def search_suppliers(self, search_term):
        """Search suppliers"""
        if not search_term:
            self.load_suppliers()
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get branch filter
            branch_filter = ""
            params = [f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"]
            if auth_manager.get_user_branch():
                branch_filter = "AND branch_id = ?"
                params.append(auth_manager.get_user_branch()['id'])
            
            cursor.execute(f'''
                SELECT id, name, contact_person, email, phone, 
                       current_balance, is_active, created_at
                FROM suppliers 
                WHERE (name LIKE ? OR contact_person LIKE ? OR email LIKE ?)
                {branch_filter}
                ORDER BY name
            ''', params)
            
            suppliers = []
            for row in cursor.fetchall():
                supplier = dict(row)
                supplier['status'] = get_text('common.active') if supplier['is_active'] else get_text('common.inactive')
                suppliers.append(supplier)
            
            self.supplier_table.load_data(suppliers)
            conn.close()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error searching suppliers: {str(e)}", "error")
    
    def on_supplier_select(self, event):
        """Handle supplier selection"""
        selected_supplier = self.supplier_table.get_selected_item()
        if selected_supplier:
            self.selected_supplier = selected_supplier
            self.load_supplier_details(selected_supplier['id'])
            
            # Enable update and delete buttons
            self.update_button.configure(state="normal")
            self.delete_button.configure(state="normal")
    
    def on_supplier_double_click(self, event):
        """Handle supplier double click"""
        selected_supplier = self.supplier_table.get_selected_item()
        if selected_supplier:
            self.show_supplier_details(selected_supplier['id'])
    
    def load_supplier_details(self, supplier_id):
        """Load supplier details into form"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM suppliers WHERE id = ?
            ''', (supplier_id,))
            
            supplier = cursor.fetchone()
            conn.close()
            
            if supplier:
                # Populate form
                self.supplier_form.set_value('name', supplier['name'])
                self.supplier_form.set_value('contact_person', supplier['contact_person'])
                self.supplier_form.set_value('email', supplier['email'])
                self.supplier_form.set_value('phone', supplier['phone'])
                self.supplier_form.set_value('address', supplier['address'])
                self.supplier_form.set_value('tax_number', supplier['tax_number'])
                self.supplier_form.set_value('payment_terms', supplier['payment_terms'])
                self.supplier_form.set_value('credit_limit', supplier['credit_limit'])
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error loading supplier details: {str(e)}", "error")
    
    def save_supplier(self):
        """Save new supplier"""
        # Check permissions
        if not auth_manager.has_permission('suppliers', 'create'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Validate form
        errors = self.supplier_form.validate_form()
        if errors:
            show_message(get_text('common.error'), '\n'.join(errors), "error")
            return
        
        try:
            # Get form data
            data = self.supplier_form.get_form_data()
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if supplier name already exists
            cursor.execute('''
                SELECT id FROM suppliers WHERE name = ? AND branch_id = ?
            ''', (data['name'], auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None))
            
            if cursor.fetchone():
                show_message(get_text('common.error'), get_text('error.supplier_exists'), "error")
                conn.close()
                return
            
            # Insert supplier
            cursor.execute('''
                INSERT INTO suppliers (
                    name, contact_person, email, phone, address, tax_number,
                    payment_terms, credit_limit, branch_id, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                data['name'], data['contact_person'], data['email'], data['phone'],
                data['address'], data['tax_number'], data['payment_terms'] or 30,
                data['credit_limit'] or 0,
                auth_manager.get_user_branch()['id'] if auth_manager.get_user_branch() else None
            ))
            
            supplier_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="create_supplier",
                table_name="suppliers",
                record_id=supplier_id,
                new_values=data
            )
            
            show_message(get_text('common.success'), get_text('success.supplier_saved'), "success")
            self.clear_form()
            self.load_suppliers()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error saving supplier: {str(e)}", "error")
    
    def update_supplier(self):
        """Update selected supplier"""
        if not self.selected_supplier:
            return
        
        # Check permissions
        if not auth_manager.has_permission('suppliers', 'update'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Validate form
        errors = self.supplier_form.validate_form()
        if errors:
            show_message(get_text('common.error'), '\n'.join(errors), "error")
            return
        
        try:
            # Get form data
            data = self.supplier_form.get_form_data()
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get old values for audit
            cursor.execute('SELECT * FROM suppliers WHERE id = ?', (self.selected_supplier['id'],))
            old_values = dict(cursor.fetchone())
            
            # Update supplier
            cursor.execute('''
                UPDATE suppliers SET
                    name = ?, contact_person = ?, email = ?, phone = ?,
                    address = ?, tax_number = ?, payment_terms = ?, credit_limit = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                data['name'], data['contact_person'], data['email'], data['phone'],
                data['address'], data['tax_number'], data['payment_terms'] or 30,
                data['credit_limit'] or 0, self.selected_supplier['id']
            ))
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="update_supplier",
                table_name="suppliers",
                record_id=self.selected_supplier['id'],
                old_values=old_values,
                new_values=data
            )
            
            show_message(get_text('common.success'), get_text('success.supplier_updated'), "success")
            self.load_suppliers()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error updating supplier: {str(e)}", "error")
    
    def delete_supplier(self):
        """Delete selected supplier"""
        if not self.selected_supplier:
            return
        
        # Check permissions
        if not auth_manager.has_permission('suppliers', 'delete'):
            show_message(get_text('common.error'), get_text('error.insufficient_permissions'), "error")
            return
        
        # Confirm deletion
        if not show_confirmation(
            get_text('common.confirm'),
            f"{get_text('common.confirm_delete')} {self.selected_supplier['name']}?"
        ):
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if supplier has transactions
            cursor.execute('''
                SELECT COUNT(*) as count FROM purchase_orders WHERE supplier_id = ?
            ''', (self.selected_supplier['id'],))
            
            if cursor.fetchone()['count'] > 0:
                # Soft delete (deactivate)
                cursor.execute('''
                    UPDATE suppliers SET is_active = 0, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (self.selected_supplier['id'],))
                message = get_text('success.supplier_deactivated')
            else:
                # Hard delete
                cursor.execute('DELETE FROM suppliers WHERE id = ?', (self.selected_supplier['id'],))
                message = get_text('success.supplier_deleted')
            
            conn.commit()
            conn.close()
            
            # Log activity
            self.db.log_activity(
                user_id=auth_manager.get_user_id(),
                action="delete_supplier",
                table_name="suppliers",
                record_id=self.selected_supplier['id']
            )
            
            show_message(get_text('common.success'), message, "success")
            self.clear_form()
            self.load_suppliers()
            
        except Exception as e:
            show_message(get_text('common.error'), f"Error deleting supplier: {str(e)}", "error")
    
    def clear_form(self):
        """Clear supplier form"""
        self.supplier_form.clear_form()
        self.selected_supplier = None
        
        # Disable update and delete buttons
        self.update_button.configure(state="disabled")
        self.delete_button.configure(state="disabled")
    
    def show_supplier_details(self, supplier_id):
        """Show detailed supplier information"""
        # Similar to customer details - implement as needed
        pass

if __name__ == "__main__":
    # Test suppliers module
    app = ctk.CTk()
    suppliers_window = SuppliersWindow(app)
    app.mainloop()
