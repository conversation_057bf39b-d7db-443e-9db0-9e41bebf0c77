#!/usr/bin/env python3
"""
Enhanced ERP Application Launcher
Professional and attractive interface
"""
import sys
import os
import traceback
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    import customtkinter as ctk
    from gui.theme_manager import theme_manager, apply_theme
    from gui.enhanced_dashboard import EnhancedDashboard
    from gui.login_window import LoginWindow
    from auth import auth_manager
    from database import DatabaseManager
    from config import Config
    from utils import get_text, setup_language
    
    # Configure modern appearance
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")
    
    class EnhancedERPApp:
        """Enhanced ERP Application with modern interface"""
        
        def __init__(self):
            self.root = None
            self.current_window = None
            
            # Initialize components
            self.init_application()
        
        def init_application(self):
            """Initialize the application"""
            try:
                print("🚀 Starting Enhanced ERP Application...")
                
                # Setup database
                print("📊 Initializing database...")
                db = DatabaseManager()
                db.init_database()
                
                # Setup language
                print("🌍 Setting up language support...")
                setup_language()
                
                # Load saved theme
                print("🎨 Loading theme...")
                theme_manager.load_saved_theme()
                
                # Show login window
                print("🔐 Showing login window...")
                self.show_login()
                
            except Exception as e:
                print(f"❌ Error initializing application: {e}")
                traceback.print_exc()
                self.show_error("Initialization Error", str(e))
        
        def show_login(self):
            """Show login window"""
            try:
                if self.current_window:
                    self.current_window.destroy()
                
                self.current_window = LoginWindow()
                
                # Override login success callback
                original_login = self.current_window.login
                
                def enhanced_login():
                    if original_login():
                        self.show_dashboard()
                
                self.current_window.login = enhanced_login
                
                # Start the main loop if not already running
                if not self.root:
                    self.current_window.mainloop()
                
            except Exception as e:
                print(f"❌ Error showing login: {e}")
                traceback.print_exc()
                self.show_error("Login Error", str(e))
        
        def show_dashboard(self):
            """Show enhanced dashboard"""
            try:
                print("🏠 Loading enhanced dashboard...")
                
                if self.current_window:
                    self.current_window.destroy()
                
                # Create enhanced dashboard
                self.current_window = EnhancedDashboard()
                
                # Override close callback
                def on_closing():
                    if self.confirm_exit():
                        self.cleanup_and_exit()
                
                self.current_window.protocol("WM_DELETE_WINDOW", on_closing)
                
                print("✅ Enhanced dashboard loaded successfully!")
                
            except Exception as e:
                print(f"❌ Error showing dashboard: {e}")
                traceback.print_exc()
                self.show_error("Dashboard Error", str(e))
                # Fallback to regular dashboard
                self.show_fallback_dashboard()
        
        def show_fallback_dashboard(self):
            """Show fallback dashboard if enhanced version fails"""
            try:
                print("🔄 Loading fallback dashboard...")
                from gui.dashboard import DashboardWindow
                
                if self.current_window:
                    self.current_window.destroy()
                
                self.current_window = DashboardWindow()
                
                def on_closing():
                    if self.confirm_exit():
                        self.cleanup_and_exit()
                
                self.current_window.protocol("WM_DELETE_WINDOW", on_closing)
                
                print("✅ Fallback dashboard loaded successfully!")
                
            except Exception as e:
                print(f"❌ Error showing fallback dashboard: {e}")
                traceback.print_exc()
                self.show_error("Critical Error", "Unable to load dashboard. Please check your installation.")
        
        def confirm_exit(self):
            """Confirm application exit"""
            try:
                import tkinter.messagebox as msgbox
                return msgbox.askyesno(
                    "تأكيد الخروج - Confirm Exit",
                    "هل تريد إغلاق التطبيق؟\\nDo you want to close the application?",
                    icon='question'
                )
            except:
                return True
        
        def cleanup_and_exit(self):
            """Cleanup and exit application"""
            try:
                print("🧹 Cleaning up...")
                
                # Logout user
                if auth_manager.is_authenticated():
                    auth_manager.logout()
                
                # Close database connections
                try:
                    db = DatabaseManager()
                    db.close_all_connections()
                except:
                    pass
                
                # Destroy current window
                if self.current_window:
                    self.current_window.destroy()
                
                print("👋 Application closed successfully!")
                
            except Exception as e:
                print(f"⚠️ Error during cleanup: {e}")
            
            finally:
                sys.exit(0)
        
        def show_error(self, title, message):
            """Show error dialog"""
            try:
                import tkinter as tk
                import tkinter.messagebox as msgbox
                
                # Create temporary root if needed
                if not self.root:
                    temp_root = tk.Tk()
                    temp_root.withdraw()
                
                msgbox.showerror(title, message)
                
                if 'temp_root' in locals():
                    temp_root.destroy()
                    
            except Exception as e:
                print(f"❌ Critical error: {e}")
                print(f"Original error: {message}")
        
        def run(self):
            """Run the application"""
            try:
                # The application is already running from show_login()
                pass
            except KeyboardInterrupt:
                print("\\n⚠️ Application interrupted by user")
                self.cleanup_and_exit()
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                traceback.print_exc()
                self.show_error("Unexpected Error", str(e))
    
    def main():
        """Main entry point"""
        try:
            print("=" * 60)
            print("🏢 Enhanced ERP System - نظام إدارة الأعمال المحسن")
            print(f"📱 Version {Config.VERSION}")
            print("=" * 60)
            
            # Check Python version
            if sys.version_info < (3, 8):
                print("❌ Python 3.8 or higher is required!")
                sys.exit(1)
            
            # Check required modules
            required_modules = [
                'customtkinter', 'PIL', 'matplotlib', 
                'pandas', 'numpy', 'sqlite3'
            ]
            
            missing_modules = []
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)
            
            if missing_modules:
                print(f"❌ Missing required modules: {', '.join(missing_modules)}")
                print("💡 Please install them using: pip install -r requirements.txt")
                sys.exit(1)
            
            # Create and run application
            app = EnhancedERPApp()
            app.run()
            
        except KeyboardInterrupt:
            print("\\n⚠️ Application interrupted by user")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Critical error starting application: {e}")
            traceback.print_exc()
            sys.exit(1)
    
    if __name__ == "__main__":
        main()

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Please make sure all required packages are installed:")
    print("   pip install customtkinter pillow matplotlib pandas numpy")
    sys.exit(1)
except Exception as e:
    print(f"❌ Critical error: {e}")
    traceback.print_exc()
    sys.exit(1)
