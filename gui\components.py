"""
Reusable GUI components for the ERP application
"""
import customtkinter as ctk
from tkinter import messagebox, ttk
import tkinter as tk
from datetime import datetime, date
from utils import get_text, is_rtl, format_arabic_text, NumberUtils, DateUtils, ValidationUtils
from config import Config

# Set appearance mode and color theme
ctk.set_appearance_mode("light")
ctk.set_default_color_theme("blue")

class BaseFrame(ctk.CTkFrame):
    """Base frame with RTL support and common functionality"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.parent = parent
        self.setup_rtl_support()
    
    def setup_rtl_support(self):
        """Setup RTL support for the frame"""
        if is_rtl():
            # Configure RTL layout
            self.grid_columnconfigure(0, weight=1)
    
    def create_label(self, text, **kwargs):
        """Create label with RTL support"""
        if is_rtl():
            text = format_arabic_text(text)
        
        label = ctk.CTkLabel(self, text=text, **kwargs)
        return label
    
    def create_button(self, text, command=None, **kwargs):
        """Create button with RTL support"""
        if is_rtl():
            text = format_arabic_text(text)
        
        button = ctk.CTkButton(self, text=text, command=command, **kwargs)
        return button
    
    def create_entry(self, placeholder_text="", **kwargs):
        """Create entry with RTL support"""
        if is_rtl() and placeholder_text:
            placeholder_text = format_arabic_text(placeholder_text)
        
        entry = ctk.CTkEntry(self, placeholder_text=placeholder_text, **kwargs)
        
        # Set text direction for RTL
        if is_rtl():
            entry.configure(justify='right')
        
        return entry

class FormFrame(BaseFrame):
    """Frame for creating forms with validation"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.fields = {}
        self.validators = {}
        self.required_fields = set()
    
    def add_field(self, name, label_text, field_type="entry", required=False, validator=None, **kwargs):
        """Add a field to the form"""
        row = len(self.fields)
        
        # Create label
        label = self.create_label(label_text)
        label.grid(row=row, column=0, sticky="e" if not is_rtl() else "w", padx=5, pady=5)
        
        # Create field based on type
        if field_type == "entry":
            field = self.create_entry(**kwargs)
        elif field_type == "password":
            field = ctk.CTkEntry(self, show="*", **kwargs)
        elif field_type == "text":
            field = ctk.CTkTextbox(self, height=100, **kwargs)
        elif field_type == "combobox":
            field = ctk.CTkComboBox(self, **kwargs)
        elif field_type == "checkbox":
            field = ctk.CTkCheckBox(self, text="", **kwargs)
        elif field_type == "date":
            field = DateEntry(self, **kwargs)
        elif field_type == "number":
            field = NumberEntry(self, **kwargs)
        else:
            field = self.create_entry(**kwargs)
        
        field.grid(row=row, column=1, sticky="ew", padx=5, pady=5)
        
        # Store field reference
        self.fields[name] = {
            'widget': field,
            'label': label,
            'type': field_type,
            'required': required
        }
        
        if required:
            self.required_fields.add(name)
            # Add asterisk to required fields
            label.configure(text=label_text + " *")
        
        if validator:
            self.validators[name] = validator
        
        # Configure grid
        self.grid_columnconfigure(1, weight=1)
        
        return field
    
    def get_value(self, name):
        """Get field value"""
        if name not in self.fields:
            return None
        
        field = self.fields[name]['widget']
        field_type = self.fields[name]['type']
        
        if field_type == "text":
            return field.get("1.0", "end-1c")
        elif field_type == "checkbox":
            return field.get()
        elif field_type == "combobox":
            return field.get()
        elif field_type == "date":
            return field.get_date()
        elif field_type == "number":
            return field.get_number()
        else:
            return field.get()
    
    def set_value(self, name, value):
        """Set field value"""
        if name not in self.fields:
            return
        
        field = self.fields[name]['widget']
        field_type = self.fields[name]['type']
        
        if field_type == "text":
            field.delete("1.0", "end")
            field.insert("1.0", str(value) if value else "")
        elif field_type == "checkbox":
            field.select() if value else field.deselect()
        elif field_type == "combobox":
            field.set(str(value) if value else "")
        elif field_type == "date":
            field.set_date(value)
        elif field_type == "number":
            field.set_number(value)
        else:
            field.delete(0, "end")
            field.insert(0, str(value) if value else "")
    
    def validate_form(self):
        """Validate all form fields"""
        errors = []
        
        # Check required fields
        for name in self.required_fields:
            value = self.get_value(name)
            if not ValidationUtils.validate_required_field(value):
                field_label = self.fields[name]['label'].cget('text').replace(' *', '')
                errors.append(f"{field_label}: {get_text('forms.required_field')}")
        
        # Run custom validators
        for name, validator in self.validators.items():
            value = self.get_value(name)
            if value and not validator(value):
                field_label = self.fields[name]['label'].cget('text').replace(' *', '')
                errors.append(f"{field_label}: {get_text('forms.invalid_value')}")
        
        return errors
    
    def get_form_data(self):
        """Get all form data as dictionary"""
        data = {}
        for name in self.fields:
            data[name] = self.get_value(name)
        return data
    
    def clear_form(self):
        """Clear all form fields"""
        for name in self.fields:
            self.set_value(name, "")

class DataTable(ctk.CTkFrame):
    """Data table with sorting and filtering"""
    
    def __init__(self, parent, columns, **kwargs):
        super().__init__(parent, **kwargs)
        self.columns = columns
        self.data = []
        self.filtered_data = []
        self.sort_column = None
        self.sort_reverse = False
        
        self.setup_table()
    
    def setup_table(self):
        """Setup the table widget"""
        # Create treeview
        self.tree = ttk.Treeview(self, columns=list(self.columns.keys()), show='headings')
        
        # Configure columns
        for col_id, col_info in self.columns.items():
            self.tree.heading(col_id, text=col_info['text'], 
                            command=lambda c=col_id: self.sort_by_column(c))
            self.tree.column(col_id, width=col_info.get('width', 100),
                           anchor=col_info.get('anchor', 'center'))
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(self, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(self, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        self.grid_rowconfigure(0, weight=1)
        self.grid_columnconfigure(0, weight=1)
        
        # Bind events
        self.tree.bind('<Double-1>', self.on_double_click)
        self.tree.bind('<Button-3>', self.on_right_click)
    
    def load_data(self, data):
        """Load data into the table"""
        self.data = data
        self.filtered_data = data.copy()
        self.refresh_table()
    
    def refresh_table(self):
        """Refresh table display"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Insert filtered data
        for row in self.filtered_data:
            values = []
            for col_id in self.columns.keys():
                value = row.get(col_id, "")
                # Format value based on column type
                if col_id in self.columns and 'format' in self.columns[col_id]:
                    formatter = self.columns[col_id]['format']
                    if formatter == 'currency':
                        value = NumberUtils.format_currency(value)
                    elif formatter == 'number':
                        value = NumberUtils.format_number(value)
                    elif formatter == 'date':
                        value = DateUtils.format_date(value)
                
                values.append(str(value))
            
            self.tree.insert('', 'end', values=values, tags=(row.get('id', ''),))
    
    def sort_by_column(self, column):
        """Sort table by column"""
        if self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False
        
        # Sort data
        self.filtered_data.sort(
            key=lambda x: x.get(column, ""),
            reverse=self.sort_reverse
        )
        
        self.refresh_table()
    
    def filter_data(self, filter_func):
        """Filter data based on function"""
        self.filtered_data = [row for row in self.data if filter_func(row)]
        self.refresh_table()
    
    def search_data(self, search_term, columns=None):
        """Search data in specified columns"""
        if not search_term:
            self.filtered_data = self.data.copy()
        else:
            search_columns = columns or list(self.columns.keys())
            self.filtered_data = []
            
            for row in self.data:
                for col in search_columns:
                    value = str(row.get(col, "")).lower()
                    if search_term.lower() in value:
                        self.filtered_data.append(row)
                        break
        
        self.refresh_table()
    
    def get_selected_item(self):
        """Get selected item data"""
        selection = self.tree.selection()
        if not selection:
            return None
        
        item = self.tree.item(selection[0])
        item_id = item['tags'][0] if item['tags'] else None
        
        # Find the row data
        for row in self.filtered_data:
            if str(row.get('id', '')) == str(item_id):
                return row
        
        return None
    
    def on_double_click(self, event):
        """Handle double click event"""
        # Override in subclass
        pass
    
    def on_right_click(self, event):
        """Handle right click event"""
        # Override in subclass
        pass

class DateEntry(ctk.CTkFrame):
    """Date entry widget"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        self.setup_widgets()
    
    def setup_widgets(self):
        """Setup date entry widgets"""
        self.date_var = tk.StringVar()
        self.date_var.set(DateUtils.format_date(DateUtils.get_current_date()))
        
        self.entry = ctk.CTkEntry(self, textvariable=self.date_var, width=120)
        self.entry.grid(row=0, column=0, padx=(0, 5))
        
        self.button = ctk.CTkButton(self, text="📅", width=30, 
                                   command=self.open_calendar)
        self.button.grid(row=0, column=1)
    
    def open_calendar(self):
        """Open calendar dialog"""
        # Simple date input dialog
        from tkinter import simpledialog
        date_str = simpledialog.askstring(
            get_text('common.date'),
            get_text('common.enter_date') + f" ({Config.DATE_FORMAT}):",
            initialvalue=self.date_var.get()
        )
        
        if date_str:
            # Validate date
            if DateUtils.parse_date(date_str):
                self.date_var.set(date_str)
    
    def get_date(self):
        """Get date value"""
        return DateUtils.parse_date(self.date_var.get())
    
    def set_date(self, date_value):
        """Set date value"""
        if date_value:
            self.date_var.set(DateUtils.format_date(date_value))
        else:
            self.date_var.set("")

class NumberEntry(ctk.CTkFrame):
    """Number entry widget with validation"""
    
    def __init__(self, parent, decimal_places=2, **kwargs):
        super().__init__(parent, **kwargs)
        self.decimal_places = decimal_places
        self.setup_widgets()
    
    def setup_widgets(self):
        """Setup number entry widget"""
        self.number_var = tk.StringVar()
        self.number_var.trace('w', self.validate_number)
        
        self.entry = ctk.CTkEntry(self, textvariable=self.number_var)
        self.entry.grid(row=0, column=0, sticky="ew")
        
        self.grid_columnconfigure(0, weight=1)
    
    def validate_number(self, *args):
        """Validate number input"""
        value = self.number_var.get()
        
        # Allow empty value
        if not value:
            return
        
        # Remove invalid characters
        valid_chars = "0123456789.-"
        filtered_value = ''.join(c for c in value if c in valid_chars)
        
        # Ensure only one decimal point
        if filtered_value.count('.') > 1:
            parts = filtered_value.split('.')
            filtered_value = parts[0] + '.' + ''.join(parts[1:])
        
        # Update if changed
        if filtered_value != value:
            self.number_var.set(filtered_value)
    
    def get_number(self):
        """Get number value"""
        try:
            return float(self.number_var.get() or 0)
        except ValueError:
            return 0
    
    def set_number(self, value):
        """Set number value"""
        if value is not None:
            formatted = f"{float(value):.{self.decimal_places}f}"
            self.number_var.set(formatted)
        else:
            self.number_var.set("")

class SearchFrame(BaseFrame):
    """Search and filter frame"""
    
    def __init__(self, parent, on_search=None, **kwargs):
        super().__init__(parent, **kwargs)
        self.on_search = on_search
        self.setup_widgets()
    
    def setup_widgets(self):
        """Setup search widgets"""
        # Search entry
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        
        search_label = self.create_label(get_text('common.search'))
        search_label.grid(row=0, column=0, padx=5, pady=5)
        
        self.search_entry = ctk.CTkEntry(self, textvariable=self.search_var, 
                                        placeholder_text=get_text('common.search'))
        self.search_entry.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # Search button
        search_button = self.create_button(get_text('common.search'), 
                                         command=self.perform_search)
        search_button.grid(row=0, column=2, padx=5, pady=5)
        
        # Clear button
        clear_button = self.create_button(get_text('common.clear'), 
                                        command=self.clear_search)
        clear_button.grid(row=0, column=3, padx=5, pady=5)
        
        self.grid_columnconfigure(1, weight=1)
    
    def on_search_change(self, *args):
        """Handle search text change"""
        if self.on_search:
            self.on_search(self.search_var.get())
    
    def perform_search(self):
        """Perform search"""
        if self.on_search:
            self.on_search(self.search_var.get())
    
    def clear_search(self):
        """Clear search"""
        self.search_var.set("")

def show_message(title, message, message_type="info"):
    """Show message dialog with RTL support"""
    if is_rtl():
        title = format_arabic_text(title)
        message = format_arabic_text(message)
    
    if message_type == "info":
        messagebox.showinfo(title, message)
    elif message_type == "warning":
        messagebox.showwarning(title, message)
    elif message_type == "error":
        messagebox.showerror(title, message)

def show_confirmation(title, message):
    """Show confirmation dialog"""
    if is_rtl():
        title = format_arabic_text(title)
        message = format_arabic_text(message)
    
    return messagebox.askyesno(title, message)

def show_input_dialog(title, prompt, initial_value=""):
    """Show input dialog"""
    from tkinter import simpledialog
    
    if is_rtl():
        title = format_arabic_text(title)
        prompt = format_arabic_text(prompt)
    
    return simpledialog.askstring(title, prompt, initialvalue=initial_value)
