#!/usr/bin/env python3
"""
Enhanced Setup Script for ERP System
إعداد محسن لنظام إدارة الأعمال
"""
import os
import sys
import subprocess
import sqlite3
from pathlib import Path
import json

def print_header():
    """Print setup header"""
    print("=" * 70)
    print("🏢 Enhanced ERP System Setup - إعداد نظام إدارة الأعمال المحسن")
    print("=" * 70)
    print()

def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"   Current version: {sys.version}")
        print("💡 Please upgrade Python from https://python.org")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - OK")
    return True

def install_requirements():
    """Install required packages"""
    print("\n📦 Installing required packages...")
    
    # Core requirements
    core_packages = [
        "customtkinter>=5.2.0",
        "Pillow>=9.0.0", 
        "matplotlib>=3.5.0",
        "pandas>=1.4.0",
        "numpy>=1.21.0",
        "python-dateutil>=2.8.0",
        "psutil>=5.8.0"
    ]
    
    # Optional packages
    optional_packages = [
        "openpyxl>=3.0.0",
        "requests>=2.25.0"
    ]
    
    try:
        # Install core packages
        print("🔧 Installing core packages...")
        for package in core_packages:
            print(f"   Installing {package.split('>=')[0]}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"⚠️ Warning: Failed to install {package}")
                print(f"   Error: {result.stderr}")
            else:
                print(f"✅ {package.split('>=')[0]} installed successfully")
        
        # Install optional packages
        print("\n🎁 Installing optional packages...")
        for package in optional_packages:
            try:
                print(f"   Installing {package.split('>=')[0]}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"✅ {package.split('>=')[0]} installed successfully")
                else:
                    print(f"⚠️ Optional package {package.split('>=')[0]} failed to install")
            except Exception as e:
                print(f"⚠️ Optional package {package.split('>=')[0]} skipped: {e}")
        
        print("\n✅ Package installation completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error installing packages: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("\n📁 Creating directories...")
    
    directories = [
        "settings",
        "backups", 
        "logs",
        "exports",
        "temp",
        "themes"
    ]
    
    try:
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
            print(f"✅ Created directory: {directory}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating directories: {e}")
        return False

def setup_database():
    """Setup database"""
    print("\n🗄️ Setting up database...")
    
    try:
        # Import and initialize database
        from database import DatabaseManager
        
        db = DatabaseManager()
        db.init_database()
        
        print("✅ Database initialized successfully")
        
        # Create sample data
        print("📊 Creating sample data...")
        create_sample_data(db)
        
        return True
        
    except Exception as e:
        print(f"❌ Error setting up database: {e}")
        return False

def create_sample_data(db):
    """Create sample data for demonstration"""
    try:
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # Sample customers
        sample_customers = [
            ("شركة الأمل للتجارة", "أحمد محمد", "<EMAIL>", "0501234567", "الرياض"),
            ("مؤسسة النور", "فاطمة علي", "<EMAIL>", "0507654321", "جدة"),
            ("شركة المستقبل", "محمد سالم", "<EMAIL>", "0509876543", "الدمام")
        ]
        
        for customer in sample_customers:
            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO customers (name, contact_person, email, phone, address)
                    VALUES (?, ?, ?, ?, ?)
                ''', customer)
            except:
                pass  # Customer might already exist
        
        # Sample suppliers
        sample_suppliers = [
            ("مورد الإلكترونيات", "سعد أحمد", "<EMAIL>", "0501111111", "الرياض"),
            ("مورد الأثاث", "نورا محمد", "<EMAIL>", "0502222222", "جدة")
        ]
        
        for supplier in sample_suppliers:
            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO suppliers (name, contact_person, email, phone, address)
                    VALUES (?, ?, ?, ?, ?)
                ''', supplier)
            except:
                pass
        
        # Sample products
        sample_products = [
            ("لابتوب ديل", "LAP001", "لابتوب ديل انسبايرون 15", 2500.00, 10, 5),
            ("كرسي مكتب", "CHR001", "كرسي مكتب مريح", 450.00, 25, 5),
            ("طابعة HP", "PRT001", "طابعة HP ليزر", 800.00, 8, 3)
        ]
        
        for product in sample_products:
            try:
                cursor.execute('''
                    INSERT OR IGNORE INTO products (name, code, description, price, quantity, min_quantity)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', product)
            except:
                pass
        
        conn.commit()
        conn.close()
        
        print("✅ Sample data created successfully")
        
    except Exception as e:
        print(f"⚠️ Warning: Could not create sample data: {e}")

def create_config_files():
    """Create configuration files"""
    print("\n⚙️ Creating configuration files...")
    
    try:
        # Default theme settings
        theme_config = {
            "current_theme": "modern_light",
            "auto_save": True,
            "language": "ar"
        }
        
        settings_dir = Path("settings")
        settings_dir.mkdir(exist_ok=True)
        
        with open(settings_dir / "theme.json", "w", encoding="utf-8") as f:
            json.dump(theme_config, f, indent=2, ensure_ascii=False)
        
        # Application settings
        app_config = {
            "version": "2.0.0",
            "auto_refresh_interval": 300,
            "max_recent_items": 10,
            "backup_enabled": True,
            "backup_interval": 24
        }
        
        with open(settings_dir / "app.json", "w", encoding="utf-8") as f:
            json.dump(app_config, f, indent=2, ensure_ascii=False)
        
        print("✅ Configuration files created")
        return True
        
    except Exception as e:
        print(f"❌ Error creating config files: {e}")
        return False

def create_shortcuts():
    """Create desktop shortcuts"""
    print("\n🔗 Creating shortcuts...")
    
    try:
        # Create batch file for easy startup
        batch_content = '''@echo off
cd /d "%~dp0"
python run_enhanced_app.py
pause'''
        
        with open("Start_Enhanced_ERP.bat", "w", encoding="utf-8") as f:
            f.write(batch_content)
        
        print("✅ Startup shortcut created: Start_Enhanced_ERP.bat")
        
        # Create Python shortcut
        python_shortcut = '''#!/usr/bin/env python3
import os
import sys
os.chdir(os.path.dirname(os.path.abspath(__file__)))
exec(open('run_enhanced_app.py').read())'''
        
        with open("start_erp.py", "w", encoding="utf-8") as f:
            f.write(python_shortcut)
        
        print("✅ Python shortcut created: start_erp.py")
        return True
        
    except Exception as e:
        print(f"⚠️ Warning: Could not create shortcuts: {e}")
        return False

def verify_installation():
    """Verify installation"""
    print("\n🔍 Verifying installation...")
    
    try:
        # Test imports
        test_imports = [
            "customtkinter",
            "PIL", 
            "matplotlib",
            "pandas",
            "numpy"
        ]
        
        for module in test_imports:
            try:
                __import__(module)
                print(f"✅ {module} - OK")
            except ImportError:
                print(f"❌ {module} - FAILED")
                return False
        
        # Test database
        if Path("erp_database.db").exists():
            print("✅ Database file - OK")
        else:
            print("❌ Database file - MISSING")
            return False
        
        # Test application files
        required_files = [
            "run_enhanced_app.py",
            "main.py",
            "database.py",
            "auth.py",
            "config.py",
            "utils.py"
        ]
        
        for file in required_files:
            if Path(file).exists():
                print(f"✅ {file} - OK")
            else:
                print(f"⚠️ {file} - MISSING (optional)")
        
        print("\n✅ Installation verification completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        return False

def print_completion_message():
    """Print completion message"""
    print("\n" + "=" * 70)
    print("🎉 Enhanced ERP System Setup Completed!")
    print("=" * 70)
    print()
    print("🚀 How to start the application:")
    print("   • Double-click: Start_Enhanced_ERP.bat")
    print("   • Command line: python run_enhanced_app.py")
    print("   • Alternative: python start_erp.py")
    print()
    print("🔐 Default login credentials:")
    print("   • Username: admin")
    print("   • Password: admin123")
    print()
    print("📚 Documentation:")
    print("   • README_ENHANCED.md - Enhanced features guide")
    print("   • DEVELOPER_GUIDE.md - Developer documentation")
    print()
    print("💡 Tips:")
    print("   • The application will create sample data on first run")
    print("   • You can change themes from the settings menu")
    print("   • Backup files are stored in the 'backups' folder")
    print()
    print("🆘 Need help?")
    print("   • Check the documentation files")
    print("   • Review the error logs in 'logs' folder")
    print()
    print("✨ Enjoy your Enhanced ERP System!")
    print("=" * 70)

def main():
    """Main setup function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        input("\nPress Enter to exit...")
        return False
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed during package installation")
        input("\nPress Enter to exit...")
        return False
    
    # Create directories
    if not create_directories():
        print("❌ Setup failed during directory creation")
        input("\nPress Enter to exit...")
        return False
    
    # Setup database
    if not setup_database():
        print("❌ Setup failed during database setup")
        input("\nPress Enter to exit...")
        return False
    
    # Create config files
    create_config_files()
    
    # Create shortcuts
    create_shortcuts()
    
    # Verify installation
    if not verify_installation():
        print("⚠️ Setup completed with warnings")
    
    # Print completion message
    print_completion_message()
    
    input("\nPress Enter to exit...")
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Setup interrupted by user")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"\n❌ Unexpected error during setup: {e}")
        input("Press Enter to exit...")
