"""
Utility functions for the ERP application
"""
import json
import os
import re
from datetime import datetime, date
from pathlib import Path
import arabic_reshaper
from bidi.algorithm import get_display
from config import Config

class LanguageManager:
    def __init__(self):
        self.current_language = Config.DEFAULT_LANGUAGE
        self.translations = {}
        self.load_translations()
    
    def load_translations(self):
        """Load all translation files"""
        for lang in Config.SUPPORTED_LANGUAGES:
            lang_file = Path(Config.LANGUAGE_DIR) / f"{lang}.json"
            if lang_file.exists():
                try:
                    with open(lang_file, 'r', encoding='utf-8') as f:
                        self.translations[lang] = json.load(f)
                except Exception as e:
                    print(f"Error loading language file {lang}: {e}")
                    self.translations[lang] = {}
            else:
                self.translations[lang] = {}
    
    def set_language(self, language):
        """Set current language"""
        if language in Config.SUPPORTED_LANGUAGES:
            self.current_language = language
            return True
        return False
    
    def get_text(self, key, language=None):
        """Get translated text by key"""
        lang = language or self.current_language
        
        if lang not in self.translations:
            lang = Config.DEFAULT_LANGUAGE
        
        # Navigate through nested keys (e.g., "login.title")
        keys = key.split('.')
        text = self.translations.get(lang, {})
        
        for k in keys:
            if isinstance(text, dict) and k in text:
                text = text[k]
            else:
                # Fallback to English if key not found
                if lang != 'en':
                    return self.get_text(key, 'en')
                return key  # Return key if translation not found
        
        return text
    
    def is_rtl(self, language=None):
        """Check if language is right-to-left"""
        lang = language or self.current_language
        return lang == 'ar'
    
    def format_arabic_text(self, text):
        """Format Arabic text for proper display"""
        if not text:
            return text
        
        # Reshape Arabic text
        reshaped_text = arabic_reshaper.reshape(text)
        # Apply bidirectional algorithm
        display_text = get_display(reshaped_text)
        return display_text

class ValidationUtils:
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone):
        """Validate phone number (Saudi format)"""
        # Remove spaces and special characters
        clean_phone = re.sub(r'[^\d+]', '', phone)
        
        # Saudi phone patterns
        patterns = [
            r'^\+966[5][0-9]{8}$',  # +966 5xxxxxxxx
            r'^966[5][0-9]{8}$',    # 966 5xxxxxxxx
            r'^05[0-9]{8}$',        # 05xxxxxxxx
            r'^5[0-9]{8}$'          # 5xxxxxxxx
        ]
        
        return any(re.match(pattern, clean_phone) for pattern in patterns)
    
    @staticmethod
    def validate_tax_number(tax_number):
        """Validate Saudi tax number"""
        if not tax_number:
            return True  # Optional field
        
        # Remove spaces and special characters
        clean_tax = re.sub(r'[^\d]', '', tax_number)
        
        # Saudi tax number is 15 digits
        return len(clean_tax) == 15 and clean_tax.isdigit()
    
    @staticmethod
    def validate_required_field(value):
        """Check if required field has value"""
        return value is not None and str(value).strip() != ''
    
    @staticmethod
    def validate_positive_number(value):
        """Check if value is a positive number"""
        try:
            num = float(value)
            return num >= 0
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_date(date_string, date_format=None):
        """Validate date format"""
        if not date_string:
            return False
        
        format_str = date_format or Config.DATE_FORMAT
        try:
            datetime.strptime(date_string, format_str)
            return True
        except ValueError:
            return False

class NumberUtils:
    @staticmethod
    def format_currency(amount, currency_symbol=None):
        """Format number as currency"""
        symbol = currency_symbol or Config.CURRENCY_SYMBOL
        try:
            formatted = f"{float(amount):,.2f}"
            return f"{formatted} {symbol}"
        except (ValueError, TypeError):
            return f"0.00 {symbol}"
    
    @staticmethod
    def format_number(number, decimal_places=2):
        """Format number with thousand separators"""
        try:
            if decimal_places == 0:
                return f"{int(float(number)):,}"
            else:
                return f"{float(number):,.{decimal_places}f}"
        except (ValueError, TypeError):
            return "0"
    
    @staticmethod
    def parse_number(number_string):
        """Parse number from string, handling Arabic numerals"""
        if not number_string:
            return 0
        
        # Convert Arabic numerals to English
        arabic_to_english = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        }
        
        english_number = str(number_string)
        for arabic, english in arabic_to_english.items():
            english_number = english_number.replace(arabic, english)
        
        # Remove thousand separators and currency symbols
        clean_number = re.sub(r'[,\s]', '', english_number)
        clean_number = re.sub(r'[^\d.-]', '', clean_number)
        
        try:
            return float(clean_number)
        except ValueError:
            return 0

class DateUtils:
    @staticmethod
    def format_date(date_obj, format_str=None):
        """Format date object to string"""
        if not date_obj:
            return ""
        
        format_str = format_str or Config.DATE_FORMAT
        
        if isinstance(date_obj, str):
            try:
                date_obj = datetime.strptime(date_obj, Config.DATE_FORMAT).date()
            except ValueError:
                return date_obj
        
        if isinstance(date_obj, datetime):
            date_obj = date_obj.date()
        
        return date_obj.strftime(format_str)
    
    @staticmethod
    def format_datetime(datetime_obj, format_str=None):
        """Format datetime object to string"""
        if not datetime_obj:
            return ""
        
        format_str = format_str or Config.DATETIME_FORMAT
        
        if isinstance(datetime_obj, str):
            try:
                datetime_obj = datetime.fromisoformat(datetime_obj)
            except ValueError:
                return datetime_obj
        
        return datetime_obj.strftime(format_str)
    
    @staticmethod
    def parse_date(date_string, format_str=None):
        """Parse date string to date object"""
        if not date_string:
            return None
        
        format_str = format_str or Config.DATE_FORMAT
        
        try:
            return datetime.strptime(date_string, format_str).date()
        except ValueError:
            return None
    
    @staticmethod
    def get_current_date():
        """Get current date"""
        return date.today()
    
    @staticmethod
    def get_current_datetime():
        """Get current datetime"""
        return datetime.now()
    
    @staticmethod
    def add_days(date_obj, days):
        """Add days to date"""
        from datetime import timedelta
        return date_obj + timedelta(days=days)

class FileUtils:
    @staticmethod
    def ensure_directory(directory_path):
        """Create directory if it doesn't exist"""
        Path(directory_path).mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def get_file_size(file_path):
        """Get file size in bytes"""
        try:
            return os.path.getsize(file_path)
        except OSError:
            return 0
    
    @staticmethod
    def format_file_size(size_bytes):
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
    
    @staticmethod
    def get_safe_filename(filename):
        """Get safe filename by removing invalid characters"""
        # Remove invalid characters for Windows/Linux
        invalid_chars = '<>:"/\\|?*'
        safe_name = filename
        for char in invalid_chars:
            safe_name = safe_name.replace(char, '_')
        
        # Remove leading/trailing spaces and dots
        safe_name = safe_name.strip(' .')
        
        # Ensure filename is not empty
        if not safe_name:
            safe_name = "untitled"
        
        return safe_name

class SecurityUtils:
    @staticmethod
    def sanitize_input(input_string):
        """Sanitize user input to prevent SQL injection"""
        if not input_string:
            return ""
        
        # Remove potentially dangerous characters
        dangerous_chars = ["'", '"', ';', '--', '/*', '*/', 'xp_', 'sp_']
        sanitized = str(input_string)
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized.strip()
    
    @staticmethod
    def generate_unique_id():
        """Generate unique ID for records"""
        import uuid
        return str(uuid.uuid4())
    
    @staticmethod
    def mask_sensitive_data(data, mask_char='*'):
        """Mask sensitive data for logging"""
        if not data:
            return ""
        
        data_str = str(data)
        if len(data_str) <= 4:
            return mask_char * len(data_str)
        
        # Show first 2 and last 2 characters
        return data_str[:2] + mask_char * (len(data_str) - 4) + data_str[-2:]

# Global language manager instance
language_manager = LanguageManager()

def get_text(key, language=None):
    """Global function to get translated text"""
    return language_manager.get_text(key, language)

def set_language(language):
    """Global function to set language"""
    return language_manager.set_language(language)

def is_rtl(language=None):
    """Global function to check RTL"""
    return language_manager.is_rtl(language)

def format_arabic_text(text):
    """Global function to format Arabic text"""
    return language_manager.format_arabic_text(text)
