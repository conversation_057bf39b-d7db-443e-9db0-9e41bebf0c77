#!/usr/bin/env python3
"""
Test Login Window
اختبار نافذة تسجيل الدخول
"""
import sys
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_login():
    """Test the login window"""
    try:
        print("🔐 Testing Enhanced Login Window...")
        
        # Import required modules
        import customtkinter as ctk
        from gui.login_window import LoginWindow
        from utils import setup_language
        from database import DatabaseManager
        
        # Setup database
        print("🗄️ Setting up database...")
        db = DatabaseManager()
        db.init_database()
        
        # Setup language
        setup_language()
        
        # Configure appearance
        ctk.set_appearance_mode("light")
        ctk.set_default_color_theme("blue")
        
        print("✅ All components loaded successfully!")
        print("\n🔐 Login Information:")
        print("   Username: admin")
        print("   Password: admin123")
        print("\n🚀 Opening login window...")
        
        # Create and show login window
        login_window = LoginWindow()
        login_window.mainloop()
        
        print("✅ Login window test completed!")
        
    except Exception as e:
        print(f"❌ Error testing login window: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login()
