@echo off
chcp 65001 >nul
title Enhanced ERP System - Fixed Login

echo.
echo ================================================================
echo 🏢 Enhanced ERP System - نظام إدارة الأعمال المحسن
echo ================================================================
echo 🔧 Fixed Login Version - إصدار نافذة الدخول المصححة
echo.

echo 🔍 Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
    echo 💡 Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found
python --version

echo.
echo 📦 Checking packages...
python -c "import customtkinter, PIL, matplotlib, pandas, numpy" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Some packages missing. Installing...
    pip install customtkinter pillow matplotlib pandas numpy
)

echo ✅ All packages ready

echo.
echo 🔐 Login Information:
echo    Username: admin
echo    Password: admin123
echo.

echo 🚀 Starting application...
echo.

REM Try different versions in order of preference
if exist "run_fixed_app.py" (
    echo 🌟 Starting Fixed Version...
    python run_fixed_app.py
) else if exist "test_login.py" (
    echo 🔧 Starting Test Version...
    python test_login.py
) else if exist "run_enhanced_app.py" (
    echo 🌟 Starting Enhanced Version...
    python run_enhanced_app.py
) else if exist "main.py" (
    echo 📱 Starting Standard Version...
    python main.py
) else (
    echo ❌ No application file found!
    pause
    exit /b 1
)

echo.
echo 👋 Application closed
pause
